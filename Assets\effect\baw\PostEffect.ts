class PostEffect
    extends Component {

    OnStart(): void {
        // Debug.Log("OnStart");
    }

    OnUpdate(): void {
    }

    OnRenderImage(src: RenderTexture, dst: RenderTexture): void
    {
        if(this.material == null)
        {
            Graphics.Blit(src, dst);
            return;
        }
        
        Graphics.BlitWithMaterial(src, dst,this.material);
    }

    public material   : Material;
}