{"prefab": {"block_count": 109, "file_map_identity": 14, "objects": [{"type": "BLOCKMAN3.GameObject", "data": {"name": "1", "enable": true, "inst_id": 90704, "file_id": 1, "layer": 0, "t": [8.9399995803833, 0.0, 0.0], "r": [0.0, 0.0, 0.0, 1.0], "s": [0.7272726893424988, 0.9090908765792847, 0.9090908765792847], "flags": 0, "is_static": false, "component_count": 0, "child_count": 2}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "Laser_blue", "enable": true, "inst_id": 90705, "file_id": 2, "layer": 0, "t": [0.6889829635620117, 1.8790059089660645, 0.96928471326828], "r": [0.0, 0.7071068286895752, 0.0, 0.7071068286895752], "s": [0.9999999403953552, 0.9999998807907104, 0.4999999701976776], "flags": 0, "is_static": false, "component_count": 1, "child_count": 2}}, {"type": "BLOCKMAN3.ParticleSystem", "data": {"materials": [{"exist": true, "sig": {"type": 2, "ptype": 2, "id": "86F49EBA64FE44ED96DBB7DE9440C25F"}, "shared": true}], "shadow_casting_mode": 1, "receive_shadows": true, "estimate_pixel_cull_type": 0, "custom_estimate_pixel_cull_value": 0.0, "inst_id": 90706, "file_id": 3, "enabled": true}}, {"type": "BLOCKMAN3.PSInvokeContext", "data": {"UVModule": {"Data": {"m_Enable": false, "m_Mode": 0, "m_TimeMode": 0, "m_FPS": 30.0, "m_SpeedRange": [0.0, 0.0], "m_TileCountX": 1, "m_TileCountY": 1, "m_AnimationType": 0, "m_RowIndex": 0, "m_CycleCount": 1.0, "m_UVChannelMask": -1, "m_RowMode": 1, "m_FrameOverTime": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_StartFrame": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_SpriteSequence": {"exist": false}}}}}, {"type": "BLOCKMAN3.PSColorModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}}}}, {"type": "BLOCKMAN3.PSColorByVelocityModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SPEED_RANGE": [0.0, 1.0]}}}, {"type": "BLOCKMAN3.PSEmissionModule", "data": {"DATA": {"ENABLE": false, "RATE_OVER_TIME": {"MAX_SCALAR": 10.0, "MIN_SCALAR": 10.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RATE_OVER_DISTANCE": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "BURSTS": []}}}, {"type": "BLOCKMAN3.PSForceModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "RANDOMIZE": false, "FORCE_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInitialModule", "data": {"DATA": {"LIFETIME": {"MAX_SCALAR": 5.0, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FLIP_ROTATION": 0.0, "MAX_PARTICLES": 0, "USE_3D_SIZE": false, "USE_3D_ROTATION": false}}}, {"type": "BLOCKMAN3.PSRotationByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSRotationModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSShapeModule", "data": {"DATA": {"ENABLE": false, "RADIUS": {"v": 1.0, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "ANGLE": 25.0, "LENGTH": 5.0, "RADIUS_THICKNESS": 1.0, "DONUT_THICKNESS": 0.20000000298023224, "BOX_THICKNESS": [0.0, 0.0, 0.0], "ARC": {"v": 360.0, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "POSITION": [0.0, 0.0, 0.0], "ROTATION": [0.0, 0.0, 0.0], "SCALE": [1.0, 1.0, 1.0], "TYPE": 2, "STYLE": 2, "ALIGN_TO_DIRECTION": false, "RANDOMIZE_DIRECTION": 0.0, "SPHERIZE_DIRECTION": 0.0, "RANDOMIZE_POSITION": 0.0}}}, {"type": "BLOCKMAN3.PSSizeByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSSizeModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSVelocityInheritModule", "data": {"DATA": {"ENABLE": false, "MODE": 0, "MULTIPLIER": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityLimitationModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "SEPARATE_AXES": false, "DRAG_MULTIPLY_BY_SIZE": true, "DRAG_MULTIPLY_BY_VELOCITY": true, "DAMPEN": 0.0, "VELOCITY": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DRAG": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "LINEAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RADIAL_VELOCITY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED_MODIFIER": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInvokeState", "data": {"START_DELAY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DELAY_ALL_LOOP": false, "DURATION": 5.0, "SIMULATION_SPEED": 1.0, "RANDOM_SEED": 1251669089, "AUTO_RANDOM_SEED": true, "LOOPING": true, "PLAY_ON_AWAKE": true, "USE_SCALED_TIME": true, "CULLING_MODE": 3, "SIMULATION_SPACE": 0, "SIMULATION_SPACE_CUSTOM": {"NULL": {"ref_type": 0}}, "EMITTER_VELOCITY_MODE": 1, "EMITTER_VELOCITY_CUSTOM": [0.0, 0.0, 0.0], "SCALING_MODE": 1}}, {"type": "BLOCKMAN3.PSRenderContext", "data": {"ENABLE": false, "STATE": {"RENDER_MODE": 0, "RENDER_SPACE": 3, "SORT_MODE": 0, "MAX_PARTICLE_SIZE": 0.5, "MIN_PARTICLE_SIZE": 0.0, "CAMERA_SCALE": 0.0, "VELOCITY_SCALE": 0.0, "LENGTH_SCALE": 2.0, "NORMAL_DIRECTION": 1.0, "PIVOT": [0.0, 0.0, 0.0], "FLIP": [0.0, 0.0, 0.0], "ALLOW_ROLL": true}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "1", "enable": true, "inst_id": 90707, "file_id": 4, "layer": 0, "t": [0.0, 0.0, 0.0], "r": [0.0, 0.0, 0.0, 1.0], "s": [1.0, 1.0, 1.0], "flags": 0, "is_static": false, "component_count": 1, "child_count": 0}}, {"type": "BLOCKMAN3.ParticleSystem", "data": {"materials": [{"exist": true, "sig": {"type": 2, "ptype": 0}, "shared": false, "data": {"head": {"_name": "01", "_type": 2}, "relys": ["93E32A3F67484F5C3086A59A98581C83"], "shader": "Engine/Default_Particle_Blend_one_oneMinusSrcAlpha", "floats": [{"name": "_AlphaThreadHold", "value": 0.5}, {"name": "_EmissiveStrength", "value": 1.0}, {"name": "_Metallic", "value": 0.0}, {"name": "_NormalStrength", "value": 1.0}, {"name": "_Smoothness", "value": 0.5}], "vectors": [{"name": "_EmissiveColor", "value": [0.0, 0.0, 0.0, 1.0]}, {"name": "_EmissiveTex_ST", "value": [1.0, 1.0, 0.0, 0.0]}, {"name": "_NormalTex_ST", "value": [1.0, 1.0, 0.0, 0.0]}], "textures": [{"name": "_MainTex", "value": {"type": 5, "ptype": 2, "id": "93E32A3F67484F5C3086A59A98581C83"}}], "origin_material": {"type": 2, "ptype": 2, "id": "C5B6E673DA8941FDC9922A3C51087754"}, "name": "01"}}], "shadow_casting_mode": 1, "receive_shadows": true, "estimate_pixel_cull_type": 0, "custom_estimate_pixel_cull_value": 0.0, "inst_id": 90708, "file_id": 5, "enabled": true}}, {"type": "BLOCKMAN3.PSInvokeContext", "data": {"UVModule": {"Data": {"m_Enable": false, "m_Mode": 0, "m_TimeMode": 0, "m_FPS": 30.0, "m_SpeedRange": [0.0, 0.0], "m_TileCountX": 1, "m_TileCountY": 1, "m_AnimationType": 0, "m_RowIndex": 0, "m_CycleCount": 1.0, "m_UVChannelMask": -1, "m_RowMode": 1, "m_FrameOverTime": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_StartFrame": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_SpriteSequence": {"exist": false}}}}}, {"type": "BLOCKMAN3.PSColorModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}}}}, {"type": "BLOCKMAN3.PSColorByVelocityModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SPEED_RANGE": [0.0, 1.0]}}}, {"type": "BLOCKMAN3.PSEmissionModule", "data": {"DATA": {"ENABLE": true, "RATE_OVER_TIME": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 10.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RATE_OVER_DISTANCE": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "BURSTS": []}}}, {"type": "BLOCKMAN3.PSForceModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "RANDOMIZE": false, "FORCE_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInitialModule", "data": {"DATA": {"LIFETIME": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED": {"MAX_SCALAR": 0.009999999776482582, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "COLOR": {"MAX_COLOR": [0.4156862795352936, 0.8627451062202454, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SIZE_X": {"MAX_SCALAR": 60.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Y": {"MAX_SCALAR": 4.5, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FLIP_ROTATION": 0.0, "MAX_PARTICLES": 1, "USE_3D_SIZE": true, "USE_3D_ROTATION": false}}}, {"type": "BLOCKMAN3.PSRotationByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSRotationModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSShapeModule", "data": {"DATA": {"ENABLE": false, "RADIUS": {"v": 1.0, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "ANGLE": 25.0, "LENGTH": 5.0, "RADIUS_THICKNESS": 1.0, "DONUT_THICKNESS": 0.20000000298023224, "BOX_THICKNESS": [0.0, 0.0, 0.0], "ARC": {"v": 360.0, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "POSITION": [0.0, 0.0, 0.0], "ROTATION": [0.0, 0.0, 0.0], "SCALE": [1.0, 1.0, 1.0], "TYPE": 2, "STYLE": 2, "ALIGN_TO_DIRECTION": false, "RANDOMIZE_DIRECTION": 0.0, "SPHERIZE_DIRECTION": 0.0, "RANDOMIZE_POSITION": 0.0}}}, {"type": "BLOCKMAN3.PSSizeByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSSizeModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSVelocityInheritModule", "data": {"DATA": {"ENABLE": false, "MODE": 0, "MULTIPLIER": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityLimitationModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "SEPARATE_AXES": false, "DRAG_MULTIPLY_BY_SIZE": true, "DRAG_MULTIPLY_BY_VELOCITY": true, "DAMPEN": 0.0, "VELOCITY": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DRAG": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "LINEAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RADIAL_VELOCITY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED_MODIFIER": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInvokeState", "data": {"START_DELAY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DELAY_ALL_LOOP": false, "DURATION": 1.0, "SIMULATION_SPEED": 1.0, "RANDOM_SEED": 1724914202, "AUTO_RANDOM_SEED": true, "LOOPING": true, "PLAY_ON_AWAKE": true, "USE_SCALED_TIME": true, "CULLING_MODE": 3, "SIMULATION_SPACE": 0, "SIMULATION_SPACE_CUSTOM": {"NULL": {"ref_type": 0}}, "EMITTER_VELOCITY_MODE": 1, "EMITTER_VELOCITY_CUSTOM": [0.0, 0.0, 0.0], "SCALING_MODE": 1}}, {"type": "BLOCKMAN3.PSRenderContext", "data": {"ENABLE": true, "STATE": {"RENDER_MODE": 1, "RENDER_SPACE": 3, "SORT_MODE": 0, "MAX_PARTICLE_SIZE": 0.5, "MIN_PARTICLE_SIZE": 0.0, "CAMERA_SCALE": 0.0, "VELOCITY_SCALE": 0.0, "LENGTH_SCALE": 2.0, "NORMAL_DIRECTION": 1.0, "PIVOT": [0.0, 0.0, 0.0], "FLIP": [0.0, 0.0, 0.0], "ALLOW_ROLL": true}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "2", "enable": true, "inst_id": 90709, "file_id": 6, "layer": 0, "t": [0.0, 0.0, -2.5043678283691406], "r": [0.0, 0.0, 0.0, 1.0], "s": [1.0, 1.0, 1.0], "flags": 0, "is_static": false, "component_count": 1, "child_count": 0}}, {"type": "BLOCKMAN3.ParticleSystem", "data": {"materials": [{"exist": true, "sig": {"type": 2, "ptype": 2, "id": "4EE3305DA053427D74AF9CC361F2E490"}, "shared": true}], "shadow_casting_mode": 1, "receive_shadows": true, "estimate_pixel_cull_type": 0, "custom_estimate_pixel_cull_value": 0.0, "inst_id": 90710, "file_id": 7, "enabled": true}}, {"type": "BLOCKMAN3.PSInvokeContext", "data": {"UVModule": {"Data": {"m_Enable": false, "m_Mode": 0, "m_TimeMode": 0, "m_FPS": 30.0, "m_SpeedRange": [0.0, 0.0], "m_TileCountX": 1, "m_TileCountY": 1, "m_AnimationType": 0, "m_RowIndex": 0, "m_CycleCount": 1.0, "m_UVChannelMask": -1, "m_RowMode": 1, "m_FrameOverTime": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_StartFrame": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_SpriteSequence": {"exist": false}}}}}, {"type": "BLOCKMAN3.PSColorModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}}}}, {"type": "BLOCKMAN3.PSColorByVelocityModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SPEED_RANGE": [0.0, 1.0]}}}, {"type": "BLOCKMAN3.PSEmissionModule", "data": {"DATA": {"ENABLE": true, "RATE_OVER_TIME": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 10.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RATE_OVER_DISTANCE": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "BURSTS": []}}}, {"type": "BLOCKMAN3.PSForceModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "RANDOMIZE": false, "FORCE_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInitialModule", "data": {"DATA": {"LIFETIME": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED": {"MAX_SCALAR": 0.009999999776482582, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "COLOR": {"MAX_COLOR": [0.12156862765550613, 0.6431372761726379, 0.800000011920929, 0.2980392277240753], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SIZE_X": {"MAX_SCALAR": 20.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Y": {"MAX_SCALAR": 3.5, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FLIP_ROTATION": 0.0, "MAX_PARTICLES": 1, "USE_3D_SIZE": true, "USE_3D_ROTATION": false}}}, {"type": "BLOCKMAN3.PSRotationByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSRotationModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSShapeModule", "data": {"DATA": {"ENABLE": false, "RADIUS": {"v": 1.0, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "ANGLE": 25.0, "LENGTH": 5.0, "RADIUS_THICKNESS": 1.0, "DONUT_THICKNESS": 0.20000000298023224, "BOX_THICKNESS": [0.0, 0.0, 0.0], "ARC": {"v": 360.0, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "POSITION": [0.0, 0.0, 0.0], "ROTATION": [0.0, 0.0, 0.0], "SCALE": [1.0, 1.0, 1.0], "TYPE": 2, "STYLE": 2, "ALIGN_TO_DIRECTION": false, "RANDOMIZE_DIRECTION": 0.0, "SPHERIZE_DIRECTION": 0.0, "RANDOMIZE_POSITION": 0.0}}}, {"type": "BLOCKMAN3.PSSizeByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSSizeModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSVelocityInheritModule", "data": {"DATA": {"ENABLE": false, "MODE": 0, "MULTIPLIER": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityLimitationModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "SEPARATE_AXES": false, "DRAG_MULTIPLY_BY_SIZE": true, "DRAG_MULTIPLY_BY_VELOCITY": true, "DAMPEN": 0.0, "VELOCITY": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DRAG": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "LINEAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RADIAL_VELOCITY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED_MODIFIER": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInvokeState", "data": {"START_DELAY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DELAY_ALL_LOOP": false, "DURATION": 1.0, "SIMULATION_SPEED": 1.0, "RANDOM_SEED": 1654553547, "AUTO_RANDOM_SEED": true, "LOOPING": true, "PLAY_ON_AWAKE": true, "USE_SCALED_TIME": true, "CULLING_MODE": 3, "SIMULATION_SPACE": 0, "SIMULATION_SPACE_CUSTOM": {"NULL": {"ref_type": 0}}, "EMITTER_VELOCITY_MODE": 1, "EMITTER_VELOCITY_CUSTOM": [0.0, 0.0, 0.0], "SCALING_MODE": 1}}, {"type": "BLOCKMAN3.PSRenderContext", "data": {"ENABLE": true, "STATE": {"RENDER_MODE": 1, "RENDER_SPACE": 3, "SORT_MODE": 0, "MAX_PARTICLE_SIZE": 0.5, "MIN_PARTICLE_SIZE": 0.0, "CAMERA_SCALE": 0.0, "VELOCITY_SCALE": 0.0, "LENGTH_SCALE": 2.0, "NORMAL_DIRECTION": 1.0, "PIVOT": [0.0, 0.0, 0.0], "FLIP": [0.0, 0.0, 0.0], "ALLOW_ROLL": true}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "Laser_blue_01", "enable": true, "inst_id": 90711, "file_id": 8, "layer": 0, "t": [0.6889830231666565, 1.2863237857818604, 0.9692847728729248], "r": [0.0, 0.7071068286895752, 0.0, 0.7071068286895752], "s": [0.9999999403953552, 0.9999998807907104, 0.4999999701976776], "flags": 0, "is_static": false, "component_count": 1, "child_count": 2}}, {"type": "BLOCKMAN3.ParticleSystem", "data": {"materials": [{"exist": true, "sig": {"type": 2, "ptype": 2, "id": "86F49EBA64FE44ED96DBB7DE9440C25F"}, "shared": true}], "shadow_casting_mode": 1, "receive_shadows": true, "estimate_pixel_cull_type": 0, "custom_estimate_pixel_cull_value": 0.0, "inst_id": 90712, "file_id": 9, "enabled": true}}, {"type": "BLOCKMAN3.PSInvokeContext", "data": {"UVModule": {"Data": {"m_Enable": false, "m_Mode": 0, "m_TimeMode": 0, "m_FPS": 30.0, "m_SpeedRange": [0.0, 0.0], "m_TileCountX": 1, "m_TileCountY": 1, "m_AnimationType": 0, "m_RowIndex": 0, "m_CycleCount": 1.0, "m_UVChannelMask": -1, "m_RowMode": 1, "m_FrameOverTime": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_StartFrame": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_SpriteSequence": {"exist": false}}}}}, {"type": "BLOCKMAN3.PSColorModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}}}}, {"type": "BLOCKMAN3.PSColorByVelocityModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SPEED_RANGE": [0.0, 1.0]}}}, {"type": "BLOCKMAN3.PSEmissionModule", "data": {"DATA": {"ENABLE": false, "RATE_OVER_TIME": {"MAX_SCALAR": 10.0, "MIN_SCALAR": 10.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RATE_OVER_DISTANCE": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "BURSTS": []}}}, {"type": "BLOCKMAN3.PSForceModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "RANDOMIZE": false, "FORCE_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInitialModule", "data": {"DATA": {"LIFETIME": {"MAX_SCALAR": 5.0, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FLIP_ROTATION": 0.0, "MAX_PARTICLES": 0, "USE_3D_SIZE": false, "USE_3D_ROTATION": false}}}, {"type": "BLOCKMAN3.PSRotationByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSRotationModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSShapeModule", "data": {"DATA": {"ENABLE": false, "RADIUS": {"v": 1.0, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "ANGLE": 25.0, "LENGTH": 5.0, "RADIUS_THICKNESS": 1.0, "DONUT_THICKNESS": 0.20000000298023224, "BOX_THICKNESS": [0.0, 0.0, 0.0], "ARC": {"v": 360.0, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "POSITION": [0.0, 0.0, 0.0], "ROTATION": [0.0, 0.0, 0.0], "SCALE": [1.0, 1.0, 1.0], "TYPE": 2, "STYLE": 2, "ALIGN_TO_DIRECTION": false, "RANDOMIZE_DIRECTION": 0.0, "SPHERIZE_DIRECTION": 0.0, "RANDOMIZE_POSITION": 0.0}}}, {"type": "BLOCKMAN3.PSSizeByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSSizeModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSVelocityInheritModule", "data": {"DATA": {"ENABLE": false, "MODE": 0, "MULTIPLIER": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityLimitationModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "SEPARATE_AXES": false, "DRAG_MULTIPLY_BY_SIZE": true, "DRAG_MULTIPLY_BY_VELOCITY": true, "DAMPEN": 0.0, "VELOCITY": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DRAG": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "LINEAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RADIAL_VELOCITY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED_MODIFIER": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInvokeState", "data": {"START_DELAY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DELAY_ALL_LOOP": false, "DURATION": 5.0, "SIMULATION_SPEED": 1.0, "RANDOM_SEED": 1638279423, "AUTO_RANDOM_SEED": true, "LOOPING": true, "PLAY_ON_AWAKE": true, "USE_SCALED_TIME": true, "CULLING_MODE": 3, "SIMULATION_SPACE": 0, "SIMULATION_SPACE_CUSTOM": {"NULL": {"ref_type": 0}}, "EMITTER_VELOCITY_MODE": 1, "EMITTER_VELOCITY_CUSTOM": [0.0, 0.0, 0.0], "SCALING_MODE": 1}}, {"type": "BLOCKMAN3.PSRenderContext", "data": {"ENABLE": false, "STATE": {"RENDER_MODE": 0, "RENDER_SPACE": 3, "SORT_MODE": 0, "MAX_PARTICLE_SIZE": 0.5, "MIN_PARTICLE_SIZE": 0.0, "CAMERA_SCALE": 0.0, "VELOCITY_SCALE": 0.0, "LENGTH_SCALE": 2.0, "NORMAL_DIRECTION": 1.0, "PIVOT": [0.0, 0.0, 0.0], "FLIP": [0.0, 0.0, 0.0], "ALLOW_ROLL": true}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "1", "enable": true, "inst_id": 90713, "file_id": 10, "layer": 0, "t": [0.0, 0.0, 0.0], "r": [0.0, 0.0, 0.0, 1.0], "s": [1.0, 1.0, 1.0], "flags": 0, "is_static": false, "component_count": 1, "child_count": 0}}, {"type": "BLOCKMAN3.ParticleSystem", "data": {"materials": [{"exist": true, "sig": {"type": 2, "ptype": 0}, "shared": false, "data": {"head": {"_name": "01", "_type": 2}, "relys": ["93E32A3F67484F5C3086A59A98581C83"], "shader": "Engine/Default_Particle_Blend_one_oneMinusSrcAlpha", "floats": [{"name": "_AlphaThreadHold", "value": 0.5}, {"name": "_EmissiveStrength", "value": 1.0}, {"name": "_Metallic", "value": 0.0}, {"name": "_NormalStrength", "value": 1.0}, {"name": "_Smoothness", "value": 0.5}], "vectors": [{"name": "_EmissiveColor", "value": [0.0, 0.0, 0.0, 1.0]}, {"name": "_EmissiveTex_ST", "value": [1.0, 1.0, 0.0, 0.0]}, {"name": "_NormalTex_ST", "value": [1.0, 1.0, 0.0, 0.0]}], "textures": [{"name": "_MainTex", "value": {"type": 5, "ptype": 2, "id": "93E32A3F67484F5C3086A59A98581C83"}}], "origin_material": {"type": 2, "ptype": 2, "id": "C5B6E673DA8941FDC9922A3C51087754"}, "name": "01"}}], "shadow_casting_mode": 1, "receive_shadows": true, "estimate_pixel_cull_type": 0, "custom_estimate_pixel_cull_value": 0.0, "inst_id": 90714, "file_id": 11, "enabled": true}}, {"type": "BLOCKMAN3.PSInvokeContext", "data": {"UVModule": {"Data": {"m_Enable": false, "m_Mode": 0, "m_TimeMode": 0, "m_FPS": 30.0, "m_SpeedRange": [0.0, 0.0], "m_TileCountX": 1, "m_TileCountY": 1, "m_AnimationType": 0, "m_RowIndex": 0, "m_CycleCount": 1.0, "m_UVChannelMask": -1, "m_RowMode": 1, "m_FrameOverTime": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_StartFrame": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_SpriteSequence": {"exist": false}}}}}, {"type": "BLOCKMAN3.PSColorModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}}}}, {"type": "BLOCKMAN3.PSColorByVelocityModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SPEED_RANGE": [0.0, 1.0]}}}, {"type": "BLOCKMAN3.PSEmissionModule", "data": {"DATA": {"ENABLE": true, "RATE_OVER_TIME": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 10.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RATE_OVER_DISTANCE": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "BURSTS": []}}}, {"type": "BLOCKMAN3.PSForceModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "RANDOMIZE": false, "FORCE_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInitialModule", "data": {"DATA": {"LIFETIME": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED": {"MAX_SCALAR": 0.009999999776482582, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "COLOR": {"MAX_COLOR": [0.16078431904315948, 0.8039215803146362, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SIZE_X": {"MAX_SCALAR": 60.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Y": {"MAX_SCALAR": 4.5, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FLIP_ROTATION": 0.0, "MAX_PARTICLES": 1, "USE_3D_SIZE": true, "USE_3D_ROTATION": false}}}, {"type": "BLOCKMAN3.PSRotationByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSRotationModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSShapeModule", "data": {"DATA": {"ENABLE": false, "RADIUS": {"v": 1.0, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "ANGLE": 25.0, "LENGTH": 5.0, "RADIUS_THICKNESS": 1.0, "DONUT_THICKNESS": 0.20000000298023224, "BOX_THICKNESS": [0.0, 0.0, 0.0], "ARC": {"v": 360.0, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "POSITION": [0.0, 0.0, 0.0], "ROTATION": [0.0, 0.0, 0.0], "SCALE": [1.0, 1.0, 1.0], "TYPE": 2, "STYLE": 2, "ALIGN_TO_DIRECTION": false, "RANDOMIZE_DIRECTION": 0.0, "SPHERIZE_DIRECTION": 0.0, "RANDOMIZE_POSITION": 0.0}}}, {"type": "BLOCKMAN3.PSSizeByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSSizeModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSVelocityInheritModule", "data": {"DATA": {"ENABLE": false, "MODE": 0, "MULTIPLIER": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityLimitationModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "SEPARATE_AXES": false, "DRAG_MULTIPLY_BY_SIZE": true, "DRAG_MULTIPLY_BY_VELOCITY": true, "DAMPEN": 0.0, "VELOCITY": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DRAG": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "LINEAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RADIAL_VELOCITY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED_MODIFIER": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInvokeState", "data": {"START_DELAY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DELAY_ALL_LOOP": false, "DURATION": 1.0, "SIMULATION_SPEED": 1.0, "RANDOM_SEED": 759644183, "AUTO_RANDOM_SEED": true, "LOOPING": true, "PLAY_ON_AWAKE": true, "USE_SCALED_TIME": true, "CULLING_MODE": 3, "SIMULATION_SPACE": 0, "SIMULATION_SPACE_CUSTOM": {"NULL": {"ref_type": 0}}, "EMITTER_VELOCITY_MODE": 1, "EMITTER_VELOCITY_CUSTOM": [0.0, 0.0, 0.0], "SCALING_MODE": 1}}, {"type": "BLOCKMAN3.PSRenderContext", "data": {"ENABLE": true, "STATE": {"RENDER_MODE": 1, "RENDER_SPACE": 3, "SORT_MODE": 0, "MAX_PARTICLE_SIZE": 0.5, "MIN_PARTICLE_SIZE": 0.0, "CAMERA_SCALE": 0.0, "VELOCITY_SCALE": 0.0, "LENGTH_SCALE": 2.0, "NORMAL_DIRECTION": 1.0, "PIVOT": [0.0, 0.0, 0.0], "FLIP": [0.0, 0.0, 0.0], "ALLOW_ROLL": true}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "2", "enable": true, "inst_id": 90715, "file_id": 12, "layer": 0, "t": [0.0, 0.0, -2.5043678283691406], "r": [0.0, 0.0, 0.0, 1.0], "s": [1.0, 1.0, 1.0], "flags": 0, "is_static": false, "component_count": 1, "child_count": 0}}, {"type": "BLOCKMAN3.ParticleSystem", "data": {"materials": [{"exist": true, "sig": {"type": 2, "ptype": 2, "id": "4EE3305DA053427D74AF9CC361F2E490"}, "shared": true}], "shadow_casting_mode": 1, "receive_shadows": true, "estimate_pixel_cull_type": 0, "custom_estimate_pixel_cull_value": 0.0, "inst_id": 90716, "file_id": 13, "enabled": true}}, {"type": "BLOCKMAN3.PSInvokeContext", "data": {"UVModule": {"Data": {"m_Enable": false, "m_Mode": 0, "m_TimeMode": 0, "m_FPS": 30.0, "m_SpeedRange": [0.0, 0.0], "m_TileCountX": 1, "m_TileCountY": 1, "m_AnimationType": 0, "m_RowIndex": 0, "m_CycleCount": 1.0, "m_UVChannelMask": -1, "m_RowMode": 1, "m_FrameOverTime": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_StartFrame": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_SpriteSequence": {"exist": false}}}}}, {"type": "BLOCKMAN3.PSColorModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}}}}, {"type": "BLOCKMAN3.PSColorByVelocityModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SPEED_RANGE": [0.0, 1.0]}}}, {"type": "BLOCKMAN3.PSEmissionModule", "data": {"DATA": {"ENABLE": true, "RATE_OVER_TIME": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 10.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RATE_OVER_DISTANCE": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "BURSTS": []}}}, {"type": "BLOCKMAN3.PSForceModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "RANDOMIZE": false, "FORCE_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInitialModule", "data": {"DATA": {"LIFETIME": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED": {"MAX_SCALAR": 0.009999999776482582, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "COLOR": {"MAX_COLOR": [0.16078431904315948, 0.8039215803146362, 1.0, 0.2980392277240753], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SIZE_X": {"MAX_SCALAR": 20.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Y": {"MAX_SCALAR": 3.5, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FLIP_ROTATION": 0.0, "MAX_PARTICLES": 1, "USE_3D_SIZE": true, "USE_3D_ROTATION": false}}}, {"type": "BLOCKMAN3.PSRotationByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSRotationModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSShapeModule", "data": {"DATA": {"ENABLE": false, "RADIUS": {"v": 1.0, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "ANGLE": 25.0, "LENGTH": 5.0, "RADIUS_THICKNESS": 1.0, "DONUT_THICKNESS": 0.20000000298023224, "BOX_THICKNESS": [0.0, 0.0, 0.0], "ARC": {"v": 360.0, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "POSITION": [0.0, 0.0, 0.0], "ROTATION": [0.0, 0.0, 0.0], "SCALE": [1.0, 1.0, 1.0], "TYPE": 2, "STYLE": 2, "ALIGN_TO_DIRECTION": false, "RANDOMIZE_DIRECTION": 0.0, "SPHERIZE_DIRECTION": 0.0, "RANDOMIZE_POSITION": 0.0}}}, {"type": "BLOCKMAN3.PSSizeByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSSizeModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSVelocityInheritModule", "data": {"DATA": {"ENABLE": false, "MODE": 0, "MULTIPLIER": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityLimitationModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "SEPARATE_AXES": false, "DRAG_MULTIPLY_BY_SIZE": true, "DRAG_MULTIPLY_BY_VELOCITY": true, "DAMPEN": 0.0, "VELOCITY": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DRAG": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "LINEAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RADIAL_VELOCITY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED_MODIFIER": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInvokeState", "data": {"START_DELAY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DELAY_ALL_LOOP": false, "DURATION": 1.0, "SIMULATION_SPEED": 1.0, "RANDOM_SEED": 781197836, "AUTO_RANDOM_SEED": true, "LOOPING": true, "PLAY_ON_AWAKE": true, "USE_SCALED_TIME": true, "CULLING_MODE": 3, "SIMULATION_SPACE": 0, "SIMULATION_SPACE_CUSTOM": {"NULL": {"ref_type": 0}}, "EMITTER_VELOCITY_MODE": 1, "EMITTER_VELOCITY_CUSTOM": [0.0, 0.0, 0.0], "SCALING_MODE": 1}}, {"type": "BLOCKMAN3.PSRenderContext", "data": {"ENABLE": true, "STATE": {"RENDER_MODE": 1, "RENDER_SPACE": 3, "SORT_MODE": 0, "MAX_PARTICLE_SIZE": 0.5, "MIN_PARTICLE_SIZE": 0.0, "CAMERA_SCALE": 0.0, "VELOCITY_SCALE": 0.0, "LENGTH_SCALE": 2.0, "NORMAL_DIRECTION": 1.0, "PIVOT": [0.0, 0.0, 0.0], "FLIP": [0.0, 0.0, 0.0], "ALLOW_ROLL": true}}}]}, "head": {"_name": "", "_type": 1}, "relys": ["C5B6E673DA8941FDC9922A3C51087754", "4EE3305DA053427D74AF9CC361F2E490", "86F49EBA64FE44ED96DBB7DE9440C25F", "00903E6839EF478E31A7BE018CBC1463", "FA71580971B64E063A8F7B90BE0DC1DE", "93E32A3F67484F5C3086A59A98581C83"]}