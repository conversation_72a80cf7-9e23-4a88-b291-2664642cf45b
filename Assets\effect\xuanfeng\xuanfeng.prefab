{"prefab": {"block_count": 19, "file_map_identity": 4, "objects": [{"type": "BLOCKMAN3.GameObject", "data": {"name": "<PERSON><PERSON><PERSON>", "enable": true, "inst_id": 191, "file_id": 1, "layer": 0, "t": [0, 0, 0], "r": [0, 0, 0, 1], "s": [1, 1, 1], "flags": 0, "is_static": false, "component_count": 0, "child_count": 1}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "<PERSON><PERSON><PERSON>", "enable": true, "inst_id": 201, "file_id": 2, "layer": 0, "t": [0, 0, 0], "r": [-0.7071068286895752, 0, 0, 0.7071068286895752], "s": [1, 1, 1], "flags": 0, "is_static": false, "component_count": 1, "child_count": 0}}, {"type": "BLOCKMAN3.ParticleSystem", "data": {"materials": [{"exist": true, "sig": {"type": 2, "ptype": 2, "id": "08BB4737B79B48020BBFA1CEDA96A7D4"}, "shared": true}], "shadow_casting_mode": 1, "receive_shadows": true, "estimate_pixel_cull_type": 0, "custom_estimate_pixel_cull_value": 0, "inst_id": 202, "file_id": 3, "enabled": true}}, {"type": "BLOCKMAN3.PSInvokeContext", "data": {"UVModule": {"Data": {"m_Enable": false, "m_Mode": 0, "m_TimeMode": 0, "m_FPS": 30, "m_SpeedRange": [0, 0], "m_TileCountX": 1, "m_TileCountY": 6, "m_AnimationType": 0, "m_RowIndex": 0, "m_CycleCount": 1, "m_UVChannelMask": -1, "m_RowMode": 1, "m_FrameOverTime": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0, "value": 0, "inTangent": 1, "outTangent": 1}, {"time": 1, "value": 1, "inTangent": 1, "outTangent": 1}]}}, "m_StartFrame": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_SpriteSequence": {"exist": false}}}}}, {"type": "BLOCKMAN3.PSColorModule", "data": {"DATA": {"ENABLE": true, "COLOR": {"MAX_COLOR": [1, 1, 1, 1], "MIN_COLOR": [1, 1, 1, 1], "MODE": 2, "MAX_GRADIENT": {"mode": 0, "alpha": [{"time": 0.08990384638309482, "value": 0, "inTangent": 0, "outTangent": 0}, {"time": 0.2723557651042938, "value": 0.23529411852359772, "inTangent": 0, "outTangent": 0}, {"time": 0.3649038672447205, "value": 0.9960784316062927, "inTangent": 0, "outTangent": 0}, {"time": 0.9466346502304077, "value": 0.9960784316062927, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 0, "inTangent": 0, "outTangent": 0}], "color": [[0.10588235408067702, 0.10588235408067702, 0.10588235408067702, 0.1269230842590332], [0.22745098173618322, 0.22745098173618322, 0.22745098173618322, 0.2485577017068863], [0.2549019753932953, 0.2549019753932953, 0.2549019753932953, 0.3622596263885498], [0.3333333432674408, 0.3333333432674408, 0.3333333432674408, 0.43629810214042664], [0.3490196168422699, 0.3490196168422699, 0.3490196168422699, 0.5923076868057251], [0.5568627715110779, 0.5568627715110779, 0.5568627715110779, 1]]}}}}}, {"type": "BLOCKMAN3.PSColorByVelocityModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1, 1, 1, 1], "MIN_COLOR": [1, 1, 1, 1], "MODE": 0}, "SPEED_RANGE": [0, 1]}}}, {"type": "BLOCKMAN3.PSEmissionModule", "data": {"DATA": {"ENABLE": true, "RATE_OVER_TIME": {"MAX_SCALAR": 150, "MIN_SCALAR": 10, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RATE_OVER_DISTANCE": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "BURSTS": []}}}, {"type": "BLOCKMAN3.PSForceModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "RANDOMIZE": false, "FORCE_X": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Y": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Z": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInitialModule", "data": {"DATA": {"LIFETIME": {"MAX_SCALAR": 0.5, "MIN_SCALAR": 5, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED": {"MAX_SCALAR": 2, "MIN_SCALAR": 5, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 0.1276073604822159, "value": 0.17607973515987402, "inTangent": 0, "outTangent": 0}]}}, "COLOR": {"MAX_COLOR": [1, 1, 1, 1], "MIN_COLOR": [1, 1, 1, 1], "MODE": 0}, "SIZE_X": {"MAX_SCALAR": 0.800000011920929, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Y": {"MAX_SCALAR": 0.800000011920929, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Z": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_X": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Y": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Z": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FLIP_ROTATION": 1, "MAX_PARTICLES": 1000, "USE_3D_SIZE": true, "USE_3D_ROTATION": false}}}, {"type": "BLOCKMAN3.PSRotationByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0, 1], "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSRotationModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": true, "ANGULAR_VELOCITY_X": {"MAX_SCALAR": -45, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSShapeModule", "data": {"DATA": {"ENABLE": true, "RADIUS": {"v": 0.029999999329447705, "m": 0, "c": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0}, "ANGLE": 45.040000915527344, "LENGTH": 5, "RADIUS_THICKNESS": 0, "DONUT_THICKNESS": 0.2000000029802322, "BOX_THICKNESS": [0, 0, 0], "ARC": {"v": 360, "m": 1, "c": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0}, "POSITION": [0, 0, 0], "ROTATION": [0, 0, 0], "SCALE": [1, 1, 1], "TYPE": 2, "STYLE": 2, "ALIGN_TO_DIRECTION": false, "RANDOMIZE_DIRECTION": 0, "SPHERIZE_DIRECTION": 0, "RANDOMIZE_POSITION": 0}}}, {"type": "BLOCKMAN3.PSSizeByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0, 1], "SIZE_X": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 1, "inTangent": 0, "outTangent": 0}]}}, "SIZE_Y": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 1, "inTangent": 0, "outTangent": 0}]}}, "SIZE_Z": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 1, "inTangent": 0, "outTangent": 0}]}}}}}, {"type": "BLOCKMAN3.PSSizeModule", "data": {"DATA": {"ENABLE": true, "SEPARATE_AXES": true, "SIZE_X": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": -1.0000000116860974e-07, "outTangent": -1.0000000116860974e-07}, {"time": 1, "value": 1, "inTangent": -1.0000000116860974e-07, "outTangent": -1.0000000116860974e-07}]}}, "SIZE_Y": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0, "value": 0.126245841383934, "inTangent": -2.000000165480742e-07, "outTangent": -2.000000165480742e-07}, {"time": 0.8764783143997192, "value": 1, "inTangent": -2.000000165480742e-07, "outTangent": -2.000000165480742e-07}]}}, "SIZE_Z": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 1, "inTangent": 0, "outTangent": 0}]}}}}}, {"type": "BLOCKMAN3.PSVelocityInheritModule", "data": {"DATA": {"ENABLE": false, "MODE": 0, "MULTIPLIER": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityLimitationModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "SEPARATE_AXES": false, "DRAG_MULTIPLY_BY_SIZE": false, "DRAG_MULTIPLY_BY_VELOCITY": false, "DAMPEN": 0, "VELOCITY": {"MAX_SCALAR": 37.7599983215332, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": -1.0000000116860974e-07, "outTangent": -1.0000000116860974e-07}, {"time": 0.795006513595581, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 0.0166112948209047, "inTangent": 0, "outTangent": 0}]}}, "VELOCITY_X": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Y": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Z": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DRAG": {"MAX_SCALAR": 0.1000000014901161, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityModule", "data": {"DATA": {"ENABLE": true, "SPACE": 0, "LINEAR_VELOCITY_X": {"MAX_SCALAR": 0, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 0.1892247051000595, "value": -0.7508305311203003, "inTangent": 0, "outTangent": 0}, {"time": 0.45335084199905396, "value": 0.9102990031242371, "inTangent": 0, "outTangent": 0}, {"time": 0.7779237627983093, "value": 0.09302324801683431, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 1, "inTangent": -2.0000000233721948e-07, "outTangent": -2.0000000233721948e-07}]}}, "LINEAR_VELOCITY_Y": {"MAX_SCALAR": 2.4000000953674316, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 1, "inTangent": 0, "outTangent": 0}]}}, "LINEAR_VELOCITY_Z": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 0.19053876399993902, "value": -0.7308970093727112, "inTangent": 0, "outTangent": 0}, {"time": 0.45072272419929504, "value": 0.9102990031242371, "inTangent": 0, "outTangent": 0}, {"time": 0.7805519104003906, "value": 0.09302324801683431, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 1, "inTangent": -2.0000000233721948e-07, "outTangent": -2.0000000233721948e-07}]}}, "ORBITAL_VELOCITY_X": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Y": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Z": {"MAX_SCALAR": -120, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RADIAL_VELOCITY": {"MAX_SCALAR": 12, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_X": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Y": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Z": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED_MODIFIER": {"MAX_SCALAR": 0.6100000143051147, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInvokeState", "data": {"START_DELAY": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DELAY_ALL_LOOP": false, "DURATION": 3, "SIMULATION_SPEED": 1, "RANDOM_SEED": 314352991, "AUTO_RANDOM_SEED": true, "LOOPING": true, "PLAY_ON_AWAKE": true, "USE_SCALED_TIME": true, "CULLING_MODE": 3, "SIMULATION_SPACE": 0, "SIMULATION_SPACE_CUSTOM": {"NULL": {"ref_type": 0}}, "EMITTER_VELOCITY_MODE": 1, "EMITTER_VELOCITY_CUSTOM": [0, 0, 0], "SCALING_MODE": 1}}, {"type": "BLOCKMAN3.PSRenderContext", "data": {"ENABLE": true, "STATE": {"RENDER_MODE": 1, "RENDER_SPACE": 3, "SORT_MODE": 0, "MAX_PARTICLE_SIZE": 0.5, "MIN_PARTICLE_SIZE": 0, "CAMERA_SCALE": 0, "VELOCITY_SCALE": 0, "LENGTH_SCALE": 2, "NORMAL_DIRECTION": 1, "PIVOT": [0, 0, 0], "FLIP": [0, 0, 0], "ALLOW_ROLL": true}}}]}, "head": {"_name": "", "_type": 1}, "relys": ["08BB4737B79B48020BBFA1CEDA96A7D4"]}