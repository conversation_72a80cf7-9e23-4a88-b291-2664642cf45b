{"prefab": {"block_count": 8, "file_map_identity": 19, "objects": [{"type": "BLOCKMAN3.GameObject", "data": {"name": "3_harmDoubleRingRoad", "enable": true, "inst_id": 9518, "file_id": 1, "layer": 0, "t": [0, 0, 0], "r": [0, 0, 0, 1], "s": [1, 1, 1], "flags": 0, "is_static": false, "component_count": 0, "child_count": 2}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "1_<PERSON><PERSON><PERSON><PERSON><PERSON>", "enable": true, "inst_id": 9519, "file_id": 2, "layer": 0, "t": [0, 0, 0], "r": [0, 0, 0, 1], "s": [1.4500000476837158, 1.4500000476837158, 1.4500000476837158], "flags": 2, "is_static": false, "prefab": {"container": {"exist": true, "sig": {"type": 1, "ptype": 2, "id": "E44755FB37FA4401F3B2F6B570667CD3"}, "shared": true}, "state": 1}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "all", "enable": true, "inst_id": 9575, "file_id": 3, "layer": 0, "t": [0, 0, 0], "r": [0, 0, 0, 1], "s": [1, 1, 1], "flags": 0, "is_static": false, "component_count": 0, "child_count": 5}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "basics", "enable": true, "inst_id": 9576, "file_id": 4, "layer": 0, "t": [-0.017094135284423804, 0.0446885749697685, 0.0529847145080566], "r": [0, 0, 0, 1], "s": [2.5, 0.300000011920929, 2.5], "flags": 2, "is_static": false, "prefab": {"container": {"exist": true, "sig": {"type": 1, "ptype": 2, "id": "C7865F6E1644467978BB855E557358EF"}, "shared": true}, "state": 1}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "basics_01", "enable": true, "inst_id": 9579, "file_id": 7, "layer": 0, "t": [-0.017093420028686503, 0.0446885749697685, -5.779707908630371], "r": [0, 0, 0, 1], "s": [2.5, 0.300000011920929, 2.5], "flags": 2, "is_static": false, "prefab": {"container": {"exist": true, "sig": {"type": 1, "ptype": 2, "id": "C7865F6E1644467978BB855E557358EF"}, "shared": true}, "state": 1}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "basics_02", "enable": true, "inst_id": 9582, "file_id": 10, "layer": 0, "t": [-0.017093621194362602, 0.0446885749697685, 5.795581817626953], "r": [0, 0, 0, 1], "s": [2.5, 0.300000011920929, 2.5], "flags": 2, "is_static": false, "prefab": {"container": {"exist": true, "sig": {"type": 1, "ptype": 2, "id": "C7865F6E1644467978BB855E557358EF"}, "shared": true}, "state": 1}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "basics_03", "enable": true, "inst_id": 9585, "file_id": 13, "layer": 0, "t": [-0.017093658447265604, 0.04468822479248051, 11.437235832214355], "r": [0, 0, 0, 1], "s": [2.5, 0.300000011920929, 2.5], "flags": 2, "is_static": false, "prefab": {"container": {"exist": true, "sig": {"type": 1, "ptype": 2, "id": "C7865F6E1644467978BB855E557358EF"}, "shared": true}, "state": 1}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "basics_04", "enable": true, "inst_id": 9588, "file_id": 16, "layer": 0, "t": [-0.017093658447265604, 0.04468822479248051, -11.312885284423828], "r": [0, 0, 0, 1], "s": [2.5, 0.300000011920929, 2.5], "flags": 2, "is_static": false, "prefab": {"container": {"exist": true, "sig": {"type": 1, "ptype": 2, "id": "C7865F6E1644467978BB855E557358EF"}, "shared": true}, "state": 1}}}]}, "head": {"_name": "", "_type": 1}, "relys": ["E44755FB37FA4401F3B2F6B570667CD3", "C7865F6E1644467978BB855E557358EF"]}