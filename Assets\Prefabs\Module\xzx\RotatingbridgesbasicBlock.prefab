{"prefab": {"block_count": 7, "file_map_identity": 8, "objects": [{"type": "BLOCKMAN3.GameObject", "data": {"name": "RotatingbridgesbasicBlock", "enable": true, "inst_id": 59308, "file_id": 1, "layer": 0, "t": [0, -1.4752731323242188, 0], "r": [0, 0, 0, 1], "s": [1, 1, 1], "flags": 0, "is_static": false, "component_count": 0, "child_count": 1}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "logic", "enable": true, "inst_id": 59309, "file_id": 2, "layer": 0, "t": [0, 0, 0], "r": [0, 0, 0, 1], "s": [1, 1, 1], "flags": 0, "is_static": false, "component_count": 2, "child_count": 1}}, {"type": "BLOCKMAN3.TSComponent", "data": {"com_type": "ObsMotion", "motionType": {"motionType": "圆周运动"}, "motionObj": {"motionObj": {"ref_type": 2, "strong_ref": 2}}, "isReturn": {"isReturn": false}, "linearInfo": {"linearInfo": "↓---直线运动参数配置---↓"}, "LinearStartPos": {}, "LinearTragetPos": {}, "motionSpeed": {"motionSpeed": 5}, "acceleration": {"acceleration": 0}, "sphereInfo": {"sphereInfo": "↓---旋转运动参数配置---↓"}, "SphereParent": {}, "sphereAsxis": {"sphereAsxis": [0, 1, 0]}, "sphereSpeed": {"sphereSpeed": 20}, "sphereInfo2": {"sphereInfo2": "----旋转往返参数配置----"}, "isSphereReturn": {"isSphereReturn": false}, "startAngle": {"startAngle": [0, 0, 0]}, "endAngle": {"endAngle": [0, 90, 0]}, "rotFactor": {"rotFactor": 1}, "inst_id": 59310, "file_id": 4, "enabled": true}}, {"type": "BLOCKMAN3.BoxCollider", "data": {"center": [0, 0.15000000596046453, 0], "size": [0.6000000238418579, 0.2700000107288361, 7.139999866485596], "material": {"exist": false}, "is_trigger": true, "inst_id": 59311, "file_id": 3, "enabled": true}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "art", "enable": true, "inst_id": 59312, "file_id": 5, "layer": 0, "t": [-0.023265123367309605, 0.007034063339233402, -0.017581939697265604], "r": [0, 0, 0, 1], "s": [0.5, 0.2000000029802322, 7], "flags": 0, "is_static": false, "component_count": 2, "child_count": 0}}, {"type": "BLOCKMAN3.Mesh<PERSON><PERSON><PERSON>", "data": {"mesh": {"exist": true, "sig": {"type": 3, "ptype": 2, "id": "2298142243C4463ABA260DC5B2EAC880"}, "shared": true}, "lmTilling": [0, 0], "lmOffset": [0, 0], "lmIndex": -1, "lmScale": 1, "orgMeshUUID": "00000000000000000000000000000000", "materials": [{"exist": true, "sig": {"type": 2, "ptype": 1, "id": "BE1E4511CFFA4735A9C455E27626F34C"}, "shared": true}], "shadow_casting_mode": 1, "receive_shadows": true, "estimate_pixel_cull_type": 0, "custom_estimate_pixel_cull_value": 0, "inst_id": 59313, "file_id": 6, "enabled": true}}, {"type": "BLOCKMAN3.BoxCollider", "data": {"center": [0, 0, 0], "size": [1, 1, 1], "material": {"exist": false}, "is_trigger": false, "inst_id": 59314, "file_id": 7, "enabled": true}}]}, "head": {"_name": "", "_type": 1}, "relys": ["2298142243C4463ABA260DC5B2EAC880"]}