{"prefab": {"block_count": 15, "file_map_identity": 17, "objects": [{"type": "BLOCKMAN3.GameObject", "data": {"name": "2_rotateTableWithBlock", "enable": true, "inst_id": 87, "file_id": 1, "layer": 0, "t": [-4.115424156188965, 0.0, -1.7132673263549805], "r": [0.0, 0.0, 0.0, 1.0], "s": [1.0, 1.0, 1.0], "flags": 0, "is_static": false, "component_count": 0, "child_count": 1}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "1_circle_motion_plane", "enable": true, "inst_id": 88, "file_id": 2, "layer": 0, "t": [0.0, -2.384185791015625e-06, 0.0], "r": [0.0, 0.0, 0.0, 1.0], "s": [15.0, 0.20000000298023224, 15.0], "flags": 0, "is_static": false, "component_count": 0, "child_count": 2}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "art", "enable": true, "inst_id": 89, "file_id": 3, "layer": 0, "t": [0.0, 0.0, 0.0], "r": [0.0, 0.0, 0.0, 1.0], "s": [1.0, 1.0, 1.0], "flags": 0, "is_static": false, "component_count": 0, "child_count": 3}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "cylinder", "enable": true, "inst_id": 90, "file_id": 4, "layer": 0, "t": [0.0, 0.0, 0.0], "r": [0.0, 0.0, 0.0, 1.0], "s": [1.0, 1.0, 1.0], "flags": 0, "is_static": false, "component_count": 2, "child_count": 0}}, {"type": "BLOCKMAN3.Mesh<PERSON><PERSON><PERSON>", "data": {"mesh": {"exist": true, "sig": {"type": 3, "ptype": 2, "id": "8C5731D5B54B4D88BD3E00B7C0A0B8A5"}, "shared": true}, "lmTilling": [0.0, 0.0], "lmOffset": [0.0, 0.0], "lmIndex": -1, "lmScale": 1.0, "orgMeshUUID": "00000000000000000000000000000000", "materials": [{"exist": true, "sig": {"type": 2, "ptype": 0}, "shared": false, "data": {"head": {"_name": "<PERSON><PERSON><PERSON>", "_type": 2}, "shader": "Engine/Default", "vectors": [{"name": "_Color", "value": [0.47843137383461, 0.26274511218070984, 0.3490196168422699, 1.0]}], "origin_material": {"type": 2, "ptype": 2, "id": "BE1E4511CFFA4735A9C455E27626F34C"}, "name": "<PERSON><PERSON><PERSON>"}}], "shadow_casting_mode": 1, "receive_shadows": true, "estimate_pixel_cull_type": 0, "custom_estimate_pixel_cull_value": 0.0, "inst_id": 91, "file_id": 5, "enabled": false}}, {"type": "BLOCKMAN3.BoxCollider", "data": {"center": [-1.7881393432617188e-07, 0.0, 2.9802322387695312e-08], "size": [1.0000001192092896, 2.0, 0.9848077297210693], "material": {"exist": false}, "is_trigger": false, "inst_id": 92, "file_id": 6, "enabled": true}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "basicroad", "enable": true, "inst_id": 93, "file_id": 7, "layer": 0, "t": [0.0, 1.1920928955078125e-05, 0.0], "r": [0.0, 0.0, 0.0, 1.0], "s": [0.06666667014360428, 5.0, 0.06666667014360428], "flags": 0, "is_static": false, "component_count": 0, "child_count": 2}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "bigbasicharm", "enable": true, "inst_id": 94, "file_id": 8, "layer": 0, "t": [-7.0, 2.5, 0.0], "r": [0.0, 0.0, 0.7071068286895752, 0.7071068286895752], "s": [5.0, 0.30000001192092896, 5.0], "flags": 2, "is_static": false, "prefab": {"container": {"exist": true, "sig": {"type": 1, "ptype": 2, "id": "0A1896D9B5ED4021719538A08D022FC3"}, "shared": true}, "state": 1}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "bigbasicharm_01", "enable": true, "inst_id": 111, "file_id": 9, "layer": 0, "t": [7.0, 2.5, 0.0], "r": [0.0, 0.0, 0.7071068286895752, 0.7071068286895752], "s": [5.0, 0.30000001192092896, 5.0], "flags": 2, "is_static": false, "prefab": {"container": {"exist": true, "sig": {"type": 1, "ptype": 2, "id": "0A1896D9B5ED4021719538A08D022FC3"}, "shared": true}, "state": 1}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "GameObject", "enable": true, "inst_id": 128, "file_id": 10, "layer": 0, "t": [0.0, -1.0, 0.0], "r": [0.0, 0.0, 0.0, 1.0], "s": [0.4000000059604645, 7.0, 0.4000000059604645], "flags": 0, "is_static": false, "component_count": 0, "child_count": 1}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "1_circle_motion_plane", "enable": true, "inst_id": 129, "file_id": 11, "layer": 0, "t": [0.0, 1.1920928955078125e-05, 0.0], "r": [-0.7071067094802856, 0.0, 0.0, 0.70710688829422], "s": [1.0, 1.0, 1.0], "flags": 2, "is_static": false, "prefab": {"container": {"exist": true, "sig": {"type": 1, "ptype": 2, "id": "FFC893816DA24C0037B1FD303C7AF283"}, "shared": true}, "state": 1}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "logic", "enable": true, "inst_id": 131, "file_id": 13, "layer": 0, "t": [0.0, 0.0, 0.0], "r": [0.0, 0.0, 0.0, 1.0], "s": [1.0, 1.0, 1.0], "flags": 0, "is_static": false, "component_count": 0, "child_count": 1}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "motionsphere", "enable": true, "inst_id": 132, "file_id": 14, "layer": 0, "t": [0.0, 1.5, 0.0], "r": [0.0, 0.0, 0.0, 1.0], "s": [1.0, 1.0, 1.0], "flags": 0, "is_static": false, "component_count": 2, "child_count": 0}}, {"type": "BLOCKMAN3.BoxCollider", "data": {"center": [0.0, 0.0, 0.0], "size": [1.0, 1.5, 1.0], "material": {"exist": false}, "is_trigger": true, "inst_id": 133, "file_id": 15, "enabled": true}}, {"type": "BLOCKMAN3.TSComponent", "data": {"com_type": "ObsMotion", "motionType": {"motionType": "圆周运动"}, "motionObj": {"motionObj": {"ref_type": 2, "strong_ref": 2}}, "isReturn": {"isReturn": false}, "linearInfo": {"linearInfo": "-----直线运动参数配置-----"}, "LinearStartPos": {}, "LinearTragetPos": {}, "motionSpeed": {"motionSpeed": 30.0}, "acceleration": {"acceleration": 0.0}, "sphereInfo": {"sphereInfo": "-----旋转运动参数配置-----"}, "SphereParent": {}, "sphereAsxis": {"sphereAsxis": [0.0, 1.0, 0.0]}, "sphereSpeed": {"sphereSpeed": 80.0}, "sphereInfo2": {"sphereInfo2": "----旋转往返参数配置----"}, "isSphereReturn": {"isSphereReturn": false}, "startAngle": {"startAngle": [0.0, 0.0, 0.0]}, "endAngle": {"endAngle": [0.0, 90.0, 0.0]}, "rotFactor": {"rotFactor": 1.0}, "inst_id": 134, "file_id": 16, "enabled": true}}]}, "head": {"_name": "", "_type": 1}, "relys": ["0A1896D9B5ED4021719538A08D022FC3", "BE1E4511CFFA4735A9C455E27626F34C", "FFC893816DA24C0037B1FD303C7AF283", "8C5731D5B54B4D88BD3E00B7C0A0B8A5"]}