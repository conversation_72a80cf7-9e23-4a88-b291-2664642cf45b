{"prefab": {"block_count": 37, "file_map_identity": 6, "objects": [{"type": "BLOCKMAN3.GameObject", "data": {"name": "float", "enable": true, "inst_id": 211, "file_id": 1, "layer": 0, "t": [0, 0, -5.435943603515626e-05], "r": [0, 0, 0, 1], "s": [1, 1, 1], "flags": 0, "is_static": false, "component_count": 0, "child_count": 2}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "feng", "enable": true, "inst_id": 197, "file_id": 2, "layer": 0, "t": [0, 0, 5.435943603515626e-05], "r": [-0.7071068286895752, 0, 0, 0.7071068286895752], "s": [1, 1, 1], "flags": 0, "is_static": false, "component_count": 1, "child_count": 0}}, {"type": "BLOCKMAN3.ParticleSystem", "data": {"materials": [{"exist": true, "sig": {"type": 2, "ptype": 2, "id": "0CD312706E194AE2CF985D2FFFF764DE"}, "shared": true}], "shadow_casting_mode": 1, "receive_shadows": true, "estimate_pixel_cull_type": 0, "custom_estimate_pixel_cull_value": 0, "inst_id": 198, "file_id": 3, "enabled": true}}, {"type": "BLOCKMAN3.PSInvokeContext", "data": {"UVModule": {"Data": {"m_Enable": false, "m_Mode": 0, "m_TimeMode": 0, "m_FPS": 30, "m_SpeedRange": [0, 0], "m_TileCountX": 1, "m_TileCountY": 1, "m_AnimationType": 0, "m_RowIndex": 0, "m_CycleCount": 1, "m_UVChannelMask": -1, "m_RowMode": 1, "m_FrameOverTime": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_StartFrame": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_SpriteSequence": {"exist": false}}}}}, {"type": "BLOCKMAN3.PSColorModule", "data": {"DATA": {"ENABLE": true, "COLOR": {"MAX_COLOR": [1, 1, 1, 1], "MIN_COLOR": [1, 1, 1, 1], "MODE": 2, "MAX_GRADIENT": {"mode": 0, "alpha": [{"time": 0.08197115361690521, "value": 0, "inTangent": 0, "outTangent": 0}, {"time": 0.19567307829856873, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 0.5579327344894409, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 0.8197115659713745, "value": 0.8235294222831726, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 0, "inTangent": 0, "outTangent": 0}], "color": [[1, 1, 1, 0], [1, 1, 1, 1]]}}}}}, {"type": "BLOCKMAN3.PSColorByVelocityModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1, 1, 1, 1], "MIN_COLOR": [1, 1, 1, 1], "MODE": 2, "MAX_GRADIENT": {"mode": 0, "alpha": [{"time": 0.8699519634246826, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 0, "inTangent": 0, "outTangent": 0}], "color": [[1, 1, 1, 0], [1, 1, 1, 1]]}}, "SPEED_RANGE": [0, 5]}}}, {"type": "BLOCKMAN3.PSEmissionModule", "data": {"DATA": {"ENABLE": true, "RATE_OVER_TIME": {"MAX_SCALAR": 0, "MIN_SCALAR": 10, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RATE_OVER_DISTANCE": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "BURSTS": [{"TIME": 0, "COUNT": {"MAX_SCALAR": 1, "MIN_SCALAR": 30, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "CYCLE": 0, "INTERVAL": 0.1000000014901161, "PROBABILITY": 1}]}}}, {"type": "BLOCKMAN3.PSForceModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "RANDOMIZE": false, "FORCE_X": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Y": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Z": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInitialModule", "data": {"DATA": {"LIFETIME": {"MAX_SCALAR": 1, "MIN_SCALAR": 5, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED": {"MAX_SCALAR": 10, "MIN_SCALAR": 10, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 0.7076411843299866, "inTangent": -0.8126617074012756, "outTangent": -0.8126617074012756}]}}, "COLOR": {"MAX_COLOR": [0, 0.2588235437870026, 0.2862745225429535, 1], "MIN_COLOR": [1, 1, 1, 0.7490196228027344], "MODE": 1}, "SIZE_X": {"MAX_SCALAR": 0.75, "MIN_SCALAR": 1.5, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1}, "SIZE_Y": {"MAX_SCALAR": 2, "MIN_SCALAR": 3, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1}, "SIZE_Z": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1}, "ROTATION_X": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Y": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Z": {"MAX_SCALAR": 1.570796251296997, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FLIP_ROTATION": 0, "MAX_PARTICLES": 1000, "USE_3D_SIZE": true, "USE_3D_ROTATION": true}}}, {"type": "BLOCKMAN3.PSRotationByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0, 1], "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSRotationModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSShapeModule", "data": {"DATA": {"ENABLE": true, "RADIUS": {"v": 0.699999988079071, "m": 0, "c": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0}, "ANGLE": 0, "LENGTH": 5, "RADIUS_THICKNESS": 0, "DONUT_THICKNESS": 0.2000000029802322, "BOX_THICKNESS": [0, 0, 0], "ARC": {"v": 360, "m": 0, "c": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0}, "POSITION": [0, 0, 0], "ROTATION": [0, 0, 0], "SCALE": [1, 1, 1], "TYPE": 2, "STYLE": 2, "ALIGN_TO_DIRECTION": false, "RANDOMIZE_DIRECTION": 0, "SPHERIZE_DIRECTION": 0, "RANDOMIZE_POSITION": 0}}}, {"type": "BLOCKMAN3.PSSizeByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0, 1], "SIZE_X": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 1, "inTangent": 0, "outTangent": 0}]}}, "SIZE_Y": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 1, "inTangent": 0, "outTangent": 0}]}}, "SIZE_Z": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 1, "inTangent": 0, "outTangent": 0}]}}}}}, {"type": "BLOCKMAN3.PSSizeModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SIZE_X": {"MAX_SCALAR": 2, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0, "value": 0.6943521499633789, "inTangent": -2.0000005918063835e-07, "outTangent": -2.0000005918063835e-07}, {"time": 1, "value": 1, "inTangent": -2.0000005918063835e-07, "outTangent": -2.0000005918063835e-07}]}}, "SIZE_Y": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 1, "inTangent": 0, "outTangent": 0}]}}, "SIZE_Z": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 1, "inTangent": 0, "outTangent": 0}]}}}}}, {"type": "BLOCKMAN3.PSVelocityInheritModule", "data": {"DATA": {"ENABLE": false, "MODE": 0, "MULTIPLIER": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityLimitationModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "SEPARATE_AXES": false, "DRAG_MULTIPLY_BY_SIZE": true, "DRAG_MULTIPLY_BY_VELOCITY": true, "DAMPEN": 0, "VELOCITY": {"MAX_SCALAR": 0, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_X": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Y": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Z": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DRAG": {"MAX_SCALAR": 0.2199999988079071, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityModule", "data": {"DATA": {"ENABLE": true, "SPACE": 0, "LINEAR_VELOCITY_X": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Y": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Z": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_X": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 3, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 1, "inTangent": 0, "outTangent": 0}]}, "MIN_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 1, "inTangent": 0, "outTangent": 0}]}}, "ORBITAL_VELOCITY_Y": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 3, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 1, "inTangent": 0, "outTangent": 0}]}, "MIN_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 1, "inTangent": 0, "outTangent": 0}]}}, "ORBITAL_VELOCITY_Z": {"MAX_SCALAR": 1, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 3, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 0.4883720874786377, "inTangent": 0, "outTangent": 0}]}, "MIN_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": -1.0000000116860974e-07, "outTangent": -1.0000000116860974e-07}, {"time": 1, "value": 1, "inTangent": -1.0000000116860974e-07, "outTangent": -1.0000000116860974e-07}]}}, "RADIAL_VELOCITY": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_X": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Y": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Z": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED_MODIFIER": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInvokeState", "data": {"START_DELAY": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DELAY_ALL_LOOP": false, "DURATION": 5, "SIMULATION_SPEED": 1, "RANDOM_SEED": 673196383, "AUTO_RANDOM_SEED": true, "LOOPING": true, "PLAY_ON_AWAKE": true, "USE_SCALED_TIME": true, "CULLING_MODE": 3, "SIMULATION_SPACE": 0, "SIMULATION_SPACE_CUSTOM": {"NULL": {"ref_type": 0}}, "EMITTER_VELOCITY_MODE": 1, "EMITTER_VELOCITY_CUSTOM": [0, 0, 0], "SCALING_MODE": 1}}, {"type": "BLOCKMAN3.PSRenderContext", "data": {"ENABLE": true, "STATE": {"RENDER_MODE": 1, "RENDER_SPACE": 3, "SORT_MODE": 0, "MAX_PARTICLE_SIZE": 0.5, "MIN_PARTICLE_SIZE": 0, "CAMERA_SCALE": 0, "VELOCITY_SCALE": 0, "LENGTH_SCALE": 2, "NORMAL_DIRECTION": 1, "PIVOT": [0, 0, 0], "FLIP": [0, 0, 0], "ALLOW_ROLL": true}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "yunwu", "enable": true, "inst_id": 199, "file_id": 4, "layer": 0, "t": [0, 0, 5.435943603515626e-05], "r": [-0.7071068286895752, 0, 0, 0.7071068286895752], "s": [1, 1, 1], "flags": 0, "is_static": false, "component_count": 1, "child_count": 0}}, {"type": "BLOCKMAN3.ParticleSystem", "data": {"materials": [{"exist": true, "sig": {"type": 2, "ptype": 2, "id": "6F3FE937205447EA09B0E6A95998176F"}, "shared": true}], "shadow_casting_mode": 1, "receive_shadows": true, "estimate_pixel_cull_type": 0, "custom_estimate_pixel_cull_value": 0, "inst_id": 200, "file_id": 5, "enabled": true}}, {"type": "BLOCKMAN3.PSInvokeContext", "data": {"UVModule": {"Data": {"m_Enable": true, "m_Mode": 0, "m_TimeMode": 0, "m_FPS": 1, "m_SpeedRange": [0, 0], "m_TileCountX": 2, "m_TileCountY": 2, "m_AnimationType": 0, "m_RowIndex": 0, "m_CycleCount": 1, "m_UVChannelMask": -1, "m_RowMode": 1, "m_FrameOverTime": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_StartFrame": {"MAX_SCALAR": 4, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1}, "m_SpriteSequence": {"exist": false}}}}}, {"type": "BLOCKMAN3.PSColorModule", "data": {"DATA": {"ENABLE": true, "COLOR": {"MAX_COLOR": [1, 1, 1, 1], "MIN_COLOR": [1, 1, 1, 1], "MODE": 2, "MAX_GRADIENT": {"mode": 0, "alpha": [{"time": 0, "value": 0, "inTangent": 0, "outTangent": 0}, {"time": 0.1586538553237915, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 0.5579327344894409, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 0.6716346144676208, "value": 0.8235294222831726, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 0, "inTangent": 0, "outTangent": 0}], "color": [[1, 1, 1, 0], [1, 1, 1, 1]]}}}}}, {"type": "BLOCKMAN3.PSColorByVelocityModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1, 1, 1, 1], "MIN_COLOR": [1, 1, 1, 1], "MODE": 2, "MAX_GRADIENT": {"mode": 0, "alpha": [{"time": 0.8699519634246826, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 0, "inTangent": 0, "outTangent": 0}], "color": [[1, 1, 1, 0], [1, 1, 1, 1]]}}, "SPEED_RANGE": [0, 5]}}}, {"type": "BLOCKMAN3.PSEmissionModule", "data": {"DATA": {"ENABLE": true, "RATE_OVER_TIME": {"MAX_SCALAR": 30, "MIN_SCALAR": 10, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RATE_OVER_DISTANCE": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "BURSTS": []}}}, {"type": "BLOCKMAN3.PSForceModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "RANDOMIZE": false, "FORCE_X": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Y": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Z": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInitialModule", "data": {"DATA": {"LIFETIME": {"MAX_SCALAR": 0.5, "MIN_SCALAR": 5, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED": {"MAX_SCALAR": 15, "MIN_SCALAR": 5, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1, "MAX_CURVE": {"curve": [{"time": 0, "value": 0.7076411843299866, "inTangent": 0, "outTangent": 0}, {"time": 0.48262545466423046, "value": 0.4717608094215393, "inTangent": 0, "outTangent": 0}]}, "MIN_CURVE": {"curve": [{"time": 0, "value": 0.31561464071273804, "inTangent": 0, "outTangent": 0}, {"time": 0.48262545466423046, "value": 0.08970099687576291, "inTangent": 0, "outTangent": 0}]}}, "COLOR": {"MAX_COLOR": [1, 1, 1, 0.14901961386203771], "MIN_COLOR": [1, 1, 1, 0.7490196228027344], "MODE": 0}, "SIZE_X": {"MAX_SCALAR": 0.300000011920929, "MIN_SCALAR": 0.8999999761581421, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1}, "SIZE_Y": {"MAX_SCALAR": 0.25, "MIN_SCALAR": 0.5, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1}, "SIZE_Z": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1}, "ROTATION_X": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Y": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Z": {"MAX_SCALAR": 1.570796251296997, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FLIP_ROTATION": 0, "MAX_PARTICLES": 1000, "USE_3D_SIZE": true, "USE_3D_ROTATION": true}}}, {"type": "BLOCKMAN3.PSRotationByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0, 1], "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSRotationModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSShapeModule", "data": {"DATA": {"ENABLE": true, "RADIUS": {"v": 0.6700000166893005, "m": 0, "c": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0}, "ANGLE": 0, "LENGTH": 5, "RADIUS_THICKNESS": 0, "DONUT_THICKNESS": 0.2000000029802322, "BOX_THICKNESS": [0, 0, 0], "ARC": {"v": 360, "m": 0, "c": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0}, "POSITION": [0, 0, 0], "ROTATION": [0, 0, 0], "SCALE": [1, 1, 1], "TYPE": 2, "STYLE": 2, "ALIGN_TO_DIRECTION": false, "RANDOMIZE_DIRECTION": 0, "SPHERIZE_DIRECTION": 0, "RANDOMIZE_POSITION": 0}}}, {"type": "BLOCKMAN3.PSSizeByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0, 1], "SIZE_X": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 1, "inTangent": 0, "outTangent": 0}]}}, "SIZE_Y": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 1, "inTangent": 0, "outTangent": 0}]}}, "SIZE_Z": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 1, "inTangent": 0, "outTangent": 0}]}}}}}, {"type": "BLOCKMAN3.PSSizeModule", "data": {"DATA": {"ENABLE": true, "SEPARATE_AXES": false, "SIZE_X": {"MAX_SCALAR": 2, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0, "value": 0.6943521499633789, "inTangent": -2.0000005918063835e-07, "outTangent": -2.0000005918063835e-07}, {"time": 1, "value": 1, "inTangent": -2.0000005918063835e-07, "outTangent": -2.0000005918063835e-07}]}}, "SIZE_Y": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 1, "inTangent": 0, "outTangent": 0}]}}, "SIZE_Z": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 1, "inTangent": 0, "outTangent": 0}]}}}}}, {"type": "BLOCKMAN3.PSVelocityInheritModule", "data": {"DATA": {"ENABLE": false, "MODE": 0, "MULTIPLIER": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityLimitationModule", "data": {"DATA": {"ENABLE": true, "SPACE": 0, "SEPARATE_AXES": false, "DRAG_MULTIPLY_BY_SIZE": true, "DRAG_MULTIPLY_BY_VELOCITY": true, "DAMPEN": 0.15000000596046453, "VELOCITY": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_X": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Y": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Z": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DRAG": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityModule", "data": {"DATA": {"ENABLE": true, "SPACE": 0, "LINEAR_VELOCITY_X": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Y": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Z": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_X": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 1, "inTangent": 0, "outTangent": 0}]}, "MIN_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 1, "inTangent": 0, "outTangent": 0}]}}, "ORBITAL_VELOCITY_Y": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 1, "inTangent": 0, "outTangent": 0}]}, "MIN_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 1, "inTangent": 0, "outTangent": 0}]}}, "ORBITAL_VELOCITY_Z": {"MAX_SCALAR": 2, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0, "MAX_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"time": 1, "value": 0.4883720874786377, "inTangent": 0, "outTangent": 0}]}, "MIN_CURVE": {"curve": [{"time": 0, "value": 1, "inTangent": -1.0000000116860974e-07, "outTangent": -1.0000000116860974e-07}, {"time": 1, "value": 1, "inTangent": -1.0000000116860974e-07, "outTangent": -1.0000000116860974e-07}]}}, "RADIAL_VELOCITY": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_X": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Y": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Z": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED_MODIFIER": {"MAX_SCALAR": 1, "MIN_SCALAR": 1, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInvokeState", "data": {"START_DELAY": {"MAX_SCALAR": 0, "MIN_SCALAR": 0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DELAY_ALL_LOOP": false, "DURATION": 5, "SIMULATION_SPEED": 1, "RANDOM_SEED": 2058819178, "AUTO_RANDOM_SEED": true, "LOOPING": true, "PLAY_ON_AWAKE": true, "USE_SCALED_TIME": true, "CULLING_MODE": 3, "SIMULATION_SPACE": 0, "SIMULATION_SPACE_CUSTOM": {"NULL": {"ref_type": 0}}, "EMITTER_VELOCITY_MODE": 1, "EMITTER_VELOCITY_CUSTOM": [0, 0, 0], "SCALING_MODE": 1}}, {"type": "BLOCKMAN3.PSRenderContext", "data": {"ENABLE": true, "STATE": {"RENDER_MODE": 0, "RENDER_SPACE": 3, "SORT_MODE": 0, "MAX_PARTICLE_SIZE": 0.5, "MIN_PARTICLE_SIZE": 0, "CAMERA_SCALE": 0, "VELOCITY_SCALE": 0, "LENGTH_SCALE": 2, "NORMAL_DIRECTION": 1, "PIVOT": [0, 0, 0], "FLIP": [0, 0, 0], "ALLOW_ROLL": true}}}]}, "head": {"_name": "", "_type": 1}, "relys": ["3C39E468861944F5AB8224FB7676AFA9", "6F3FE937205447EA09B0E6A95998176F", "387B85E97C6E4964EE9ECB24E35EBD2A", "0CD312706E194AE2CF985D2FFFF764DE"]}