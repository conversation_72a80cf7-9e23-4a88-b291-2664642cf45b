{"prefab": {"block_count": 126, "file_map_identity": 15, "objects": [{"type": "BLOCKMAN3.GameObject", "data": {"name": "ParticleSystemeffect", "enable": true, "inst_id": 21918, "file_id": 1, "layer": 0, "t": [0.0, 0.0, 0.0], "r": [0.0, 0.0, 0.0, 1.0], "s": [1.0, 1.0, 1.0], "flags": 0, "is_static": false, "component_count": 1, "child_count": 6}}, {"type": "BLOCKMAN3.ParticleSystem", "data": {"materials": [{"exist": true, "sig": {"type": 2, "ptype": 2, "id": "86F49EBA64FE44ED96DBB7DE9440C25F"}, "shared": true}], "shadow_casting_mode": 1, "receive_shadows": true, "estimate_pixel_cull_type": 0, "custom_estimate_pixel_cull_value": 0.0, "inst_id": 21919, "file_id": 2, "enabled": true}}, {"type": "BLOCKMAN3.PSInvokeContext", "data": {"UVModule": {"Data": {"m_Enable": false, "m_Mode": 0, "m_TimeMode": 0, "m_FPS": 30.0, "m_SpeedRange": [0.0, 0.0], "m_TileCountX": 1, "m_TileCountY": 1, "m_AnimationType": 0, "m_RowIndex": 0, "m_CycleCount": 1.0, "m_UVChannelMask": -1, "m_RowMode": 1, "m_FrameOverTime": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_StartFrame": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_SpriteSequence": {"exist": false}}}}}, {"type": "BLOCKMAN3.PSColorModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}}}}, {"type": "BLOCKMAN3.PSColorByVelocityModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SPEED_RANGE": [0.0, 1.0]}}}, {"type": "BLOCKMAN3.PSEmissionModule", "data": {"DATA": {"ENABLE": false, "RATE_OVER_TIME": {"MAX_SCALAR": 10.0, "MIN_SCALAR": 10.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RATE_OVER_DISTANCE": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "BURSTS": []}}}, {"type": "BLOCKMAN3.PSForceModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "RANDOMIZE": false, "FORCE_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInitialModule", "data": {"DATA": {"LIFETIME": {"MAX_SCALAR": 5.0, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED": {"MAX_SCALAR": 5.0, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FLIP_ROTATION": 0.0, "MAX_PARTICLES": 1000, "USE_3D_SIZE": false, "USE_3D_ROTATION": false}}}, {"type": "BLOCKMAN3.PSRotationByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSRotationModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSShapeModule", "data": {"DATA": {"ENABLE": false, "RADIUS": {"v": 1.0, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "ANGLE": 25.0, "LENGTH": 5.0, "RADIUS_THICKNESS": 1.0, "DONUT_THICKNESS": 0.20000000298023224, "BOX_THICKNESS": [0.0, 0.0, 0.0], "ARC": {"v": 360.0, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "POSITION": [0.0, 0.0, 0.0], "ROTATION": [0.0, 0.0, 0.0], "SCALE": [1.0, 1.0, 1.0], "TYPE": 3, "STYLE": 2, "ALIGN_TO_DIRECTION": false, "RANDOMIZE_DIRECTION": 0.0, "SPHERIZE_DIRECTION": 0.0, "RANDOMIZE_POSITION": 0.0}}}, {"type": "BLOCKMAN3.PSSizeByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSSizeModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSVelocityInheritModule", "data": {"DATA": {"ENABLE": false, "MODE": 0, "MULTIPLIER": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityLimitationModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "SEPARATE_AXES": false, "DRAG_MULTIPLY_BY_SIZE": true, "DRAG_MULTIPLY_BY_VELOCITY": true, "DAMPEN": 0.0, "VELOCITY": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DRAG": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "LINEAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RADIAL_VELOCITY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED_MODIFIER": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInvokeState", "data": {"START_DELAY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DELAY_ALL_LOOP": false, "DURATION": 5.0, "SIMULATION_SPEED": 1.0, "RANDOM_SEED": 925150908, "AUTO_RANDOM_SEED": true, "LOOPING": false, "PLAY_ON_AWAKE": true, "USE_SCALED_TIME": true, "CULLING_MODE": 3, "SIMULATION_SPACE": 0, "SIMULATION_SPACE_CUSTOM": {"NULL": {"ref_type": 0}}, "EMITTER_VELOCITY_MODE": 1, "EMITTER_VELOCITY_CUSTOM": [0.0, 0.0, 0.0], "SCALING_MODE": 1}}, {"type": "BLOCKMAN3.PSRenderContext", "data": {"ENABLE": true, "STATE": {"RENDER_MODE": 0, "RENDER_SPACE": 3, "SORT_MODE": 0, "MAX_PARTICLE_SIZE": 0.5, "MIN_PARTICLE_SIZE": 0.0, "CAMERA_SCALE": 0.0, "VELOCITY_SCALE": 0.0, "LENGTH_SCALE": 2.0, "NORMAL_DIRECTION": 1.0, "PIVOT": [0.0, 0.0, 0.0], "FLIP": [0.0, 0.0, 0.0], "ALLOW_ROLL": true}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "1", "enable": true, "inst_id": 21920, "file_id": 3, "layer": 0, "t": [-1.166974663734436, 0.5013210773468018, 1.0556373596191406], "r": [0.0, 0.0, 0.0, 1.0], "s": [1.0, 1.0, 1.0], "flags": 0, "is_static": false, "component_count": 1, "child_count": 0}}, {"type": "BLOCKMAN3.ParticleSystem", "data": {"materials": [{"exist": true, "sig": {"type": 2, "ptype": 0}, "shared": false, "data": {"head": {"_name": "1", "_type": 2}, "relys": ["9B79E9C70BEC432C4E80728249DEEDD1"], "shader": "Engine/Default_Particle_Blend_one_oneMinusSrcAlpha", "floats": [{"name": "_AlphaThreadHold", "value": 0.5}, {"name": "_EmissiveStrength", "value": 1.0}, {"name": "_Metallic", "value": 0.0}, {"name": "_NormalStrength", "value": 1.0}, {"name": "_Smoothness", "value": 0.5}], "vectors": [{"name": "_CustomCol", "value": [1.0, 1.0, 1.0, 1.0]}, {"name": "_EmissiveColor", "value": [0.0, 0.0, 0.0, 1.0]}, {"name": "_EmissiveTex_ST", "value": [1.0, 1.0, 0.0, 0.0]}, {"name": "_NormalTex_ST", "value": [1.0, 1.0, 0.0, 0.0]}], "textures": [{"name": "_MainTex", "value": {"type": 5, "ptype": 2, "id": "9B79E9C70BEC432C4E80728249DEEDD1"}}], "origin_material": {"type": 2, "ptype": 2, "id": "788C3A0D330E46E3A699BB48D311E6CD"}, "name": "1"}}], "shadow_casting_mode": 1, "receive_shadows": true, "estimate_pixel_cull_type": 0, "custom_estimate_pixel_cull_value": 0.0, "inst_id": 21921, "file_id": 4, "enabled": true}}, {"type": "BLOCKMAN3.PSInvokeContext", "data": {"UVModule": {"Data": {"m_Enable": true, "m_Mode": 0, "m_TimeMode": 2, "m_FPS": 18.0, "m_SpeedRange": [0.0, 0.0], "m_TileCountX": 4, "m_TileCountY": 4, "m_AnimationType": 0, "m_RowIndex": 0, "m_CycleCount": 100.0, "m_UVChannelMask": -1, "m_RowMode": 1, "m_FrameOverTime": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 0.0, "inTangent": 1.0, "outTangent": 1.0}, {"time": 1.0, "value": 1.0, "inTangent": 1.0, "outTangent": 1.0}]}}, "m_StartFrame": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_SpriteSequence": {"exist": false}}}}}, {"type": "BLOCKMAN3.PSColorModule", "data": {"DATA": {"ENABLE": true, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 2, "MAX_GRADIENT": {"mode": 0, "alpha": [{"time": 0.05000000074505806, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 0.8091346621513367, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 0.0, "inTangent": 0.0, "outTangent": 0.0}], "color": [[1.0, 1.0, 1.0, 0.0], [1.0, 1.0, 1.0, 1.0]]}}}}}, {"type": "BLOCKMAN3.PSColorByVelocityModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SPEED_RANGE": [0.0, 1.0]}}}, {"type": "BLOCKMAN3.PSEmissionModule", "data": {"DATA": {"ENABLE": true, "RATE_OVER_TIME": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 10.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RATE_OVER_DISTANCE": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "BURSTS": [{"TIME": 0.0, "COUNT": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 30.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "CYCLE": 0, "INTERVAL": 1.2000000476837158, "PROBABILITY": 1.0}]}}}, {"type": "BLOCKMAN3.PSForceModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "RANDOMIZE": false, "FORCE_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInitialModule", "data": {"DATA": {"LIFETIME": {"MAX_SCALAR": 3.0, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "COLOR": {"MAX_COLOR": [0.8745098114013672, 0.3529411852359772, 0.9215686321258545, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SIZE_X": {"MAX_SCALAR": 1.2999999523162842, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FLIP_ROTATION": 0.0, "MAX_PARTICLES": 1000, "USE_3D_SIZE": false, "USE_3D_ROTATION": false}}}, {"type": "BLOCKMAN3.PSRotationByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSRotationModule", "data": {"DATA": {"ENABLE": true, "SEPARATE_AXES": true, "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": -0.48172757029533386, "inTangent": -2.0000000233721948e-07, "outTangent": -2.0000000233721948e-07}, {"time": 0.19973719120025635, "value": 0.5182723999023438, "inTangent": 0.0, "outTangent": 0.0}, {"time": 0.3994743525981903, "value": -0.5049834251403809, "inTangent": 0.0, "outTangent": 0.0}, {"time": 0.6005255579948425, "value": 0.5049834251403809, "inTangent": 0.0, "outTangent": 0.0}, {"time": 0.8015768527984619, "value": -0.5116279125213623, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 0.4883720874786377, "inTangent": -2.0000000233721948e-07, "outTangent": -2.0000000233721948e-07}]}}}}}, {"type": "BLOCKMAN3.PSShapeModule", "data": {"DATA": {"ENABLE": false, "RADIUS": {"v": 9.999999747378752e-05, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "ANGLE": 0.0, "LENGTH": 5.0, "RADIUS_THICKNESS": 1.0, "DONUT_THICKNESS": 0.20000000298023224, "BOX_THICKNESS": [0.0, 0.0, 0.0], "ARC": {"v": 360.0, "m": 2, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "POSITION": [0.0, 0.0, 0.0], "ROTATION": [0.0, 0.0, 0.0], "SCALE": [1.0, 1.0, 1.0], "TYPE": 2, "STYLE": 2, "ALIGN_TO_DIRECTION": false, "RANDOMIZE_DIRECTION": 0.0, "SPHERIZE_DIRECTION": 0.0, "RANDOMIZE_POSITION": 0.0}}}, {"type": "BLOCKMAN3.PSSizeByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSSizeModule", "data": {"DATA": {"ENABLE": true, "SEPARATE_AXES": false, "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 0.4883720874786377, "inTangent": 1.1038789749145508, "outTangent": 1.1038789749145508}, {"time": 0.7923784852027893, "value": 1.0, "inTangent": -2.0000004496978363e-07, "outTangent": -2.0000004496978363e-07}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSVelocityInheritModule", "data": {"DATA": {"ENABLE": false, "MODE": 0, "MULTIPLIER": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityLimitationModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "SEPARATE_AXES": false, "DRAG_MULTIPLY_BY_SIZE": true, "DRAG_MULTIPLY_BY_VELOCITY": true, "DAMPEN": 0.0, "VELOCITY": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DRAG": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityModule", "data": {"DATA": {"ENABLE": true, "SPACE": 0, "LINEAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Y": {"MAX_SCALAR": -0.20000000298023224, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RADIAL_VELOCITY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED_MODIFIER": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInvokeState", "data": {"START_DELAY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DELAY_ALL_LOOP": false, "DURATION": 5.0, "SIMULATION_SPEED": 1.0, "RANDOM_SEED": 1418732249, "AUTO_RANDOM_SEED": true, "LOOPING": true, "PLAY_ON_AWAKE": true, "USE_SCALED_TIME": true, "CULLING_MODE": 3, "SIMULATION_SPACE": 0, "SIMULATION_SPACE_CUSTOM": {"NULL": {"ref_type": 0}}, "EMITTER_VELOCITY_MODE": 1, "EMITTER_VELOCITY_CUSTOM": [0.0, 0.0, 0.0], "SCALING_MODE": 1}}, {"type": "BLOCKMAN3.PSRenderContext", "data": {"ENABLE": true, "STATE": {"RENDER_MODE": 2, "RENDER_SPACE": 3, "SORT_MODE": 0, "MAX_PARTICLE_SIZE": 0.5, "MIN_PARTICLE_SIZE": 0.0, "CAMERA_SCALE": 0.0, "VELOCITY_SCALE": 0.0, "LENGTH_SCALE": 2.0, "NORMAL_DIRECTION": 1.0, "PIVOT": [0.0, 0.0, 0.0], "FLIP": [0.0, 0.0, 0.0], "ALLOW_ROLL": true}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "2", "enable": true, "inst_id": 21922, "file_id": 5, "layer": 0, "t": [1.1302266120910645, 0.46193814277648926, -1.097440242767334], "r": [0.7071067094802856, 0.0, 0.0, 0.7071068286895752], "s": [1.0000001192092896, 1.0, 1.0], "flags": 0, "is_static": false, "component_count": 1, "child_count": 0}}, {"type": "BLOCKMAN3.ParticleSystem", "data": {"materials": [{"exist": true, "sig": {"type": 2, "ptype": 0}, "shared": false, "data": {"head": {"_name": "1", "_type": 2}, "relys": ["9B79E9C70BEC432C4E80728249DEEDD1"], "shader": "Engine/Default_Particle_Blend_one_oneMinusSrcAlpha", "floats": [{"name": "_AlphaThreadHold", "value": 0.5}, {"name": "_EmissiveStrength", "value": 1.0}, {"name": "_Metallic", "value": 0.0}, {"name": "_NormalStrength", "value": 1.0}, {"name": "_Smoothness", "value": 0.5}], "vectors": [{"name": "_EmissiveColor", "value": [0.0, 0.0, 0.0, 1.0]}, {"name": "_EmissiveTex_ST", "value": [1.0, 1.0, 0.0, 0.0]}, {"name": "_NormalTex_ST", "value": [1.0, 1.0, 0.0, 0.0]}], "textures": [{"name": "_MainTex", "value": {"type": 5, "ptype": 2, "id": "9B79E9C70BEC432C4E80728249DEEDD1"}}], "origin_material": {"type": 2, "ptype": 2, "id": "788C3A0D330E46E3A699BB48D311E6CD"}, "name": "1"}}], "shadow_casting_mode": 1, "receive_shadows": true, "estimate_pixel_cull_type": 0, "custom_estimate_pixel_cull_value": 0.0, "inst_id": 21923, "file_id": 6, "enabled": true}}, {"type": "BLOCKMAN3.PSInvokeContext", "data": {"UVModule": {"Data": {"m_Enable": true, "m_Mode": 0, "m_TimeMode": 2, "m_FPS": 18.0, "m_SpeedRange": [0.0, 0.0], "m_TileCountX": 4, "m_TileCountY": 4, "m_AnimationType": 0, "m_RowIndex": 0, "m_CycleCount": 100.0, "m_UVChannelMask": -1, "m_RowMode": 1, "m_FrameOverTime": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 0.0, "inTangent": 1.0, "outTangent": 1.0}, {"time": 1.0, "value": 1.0, "inTangent": 1.0, "outTangent": 1.0}]}}, "m_StartFrame": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_SpriteSequence": {"exist": false}}}}}, {"type": "BLOCKMAN3.PSColorModule", "data": {"DATA": {"ENABLE": true, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 2, "MAX_GRADIENT": {"mode": 0, "alpha": [{"time": 0.05000000074505806, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 0.8091346621513367, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 0.0, "inTangent": 0.0, "outTangent": 0.0}], "color": [[1.0, 1.0, 1.0, 0.0], [1.0, 1.0, 1.0, 1.0]]}}}}}, {"type": "BLOCKMAN3.PSColorByVelocityModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SPEED_RANGE": [0.0, 1.0]}}}, {"type": "BLOCKMAN3.PSEmissionModule", "data": {"DATA": {"ENABLE": true, "RATE_OVER_TIME": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 10.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RATE_OVER_DISTANCE": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "BURSTS": [{"TIME": 0.0, "COUNT": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 30.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "CYCLE": 0, "INTERVAL": 1.2000000476837158, "PROBABILITY": 1.0}]}}}, {"type": "BLOCKMAN3.PSForceModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "RANDOMIZE": false, "FORCE_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInitialModule", "data": {"DATA": {"LIFETIME": {"MAX_SCALAR": 3.0, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "COLOR": {"MAX_COLOR": [0.8745098114013672, 0.3529411852359772, 0.9215686321258545, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SIZE_X": {"MAX_SCALAR": 1.2999999523162842, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FLIP_ROTATION": 0.0, "MAX_PARTICLES": 1000, "USE_3D_SIZE": false, "USE_3D_ROTATION": false}}}, {"type": "BLOCKMAN3.PSRotationByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSRotationModule", "data": {"DATA": {"ENABLE": true, "SEPARATE_AXES": true, "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": -0.48172757029533386, "inTangent": -2.0000000233721948e-07, "outTangent": -2.0000000233721948e-07}, {"time": 0.19973719120025635, "value": 0.5182723999023438, "inTangent": 0.0, "outTangent": 0.0}, {"time": 0.3994743525981903, "value": -0.5049834251403809, "inTangent": 0.0, "outTangent": 0.0}, {"time": 0.6005255579948425, "value": 0.5049834251403809, "inTangent": 0.0, "outTangent": 0.0}, {"time": 0.8015768527984619, "value": -0.5116279125213623, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 0.4883720874786377, "inTangent": -2.0000000233721948e-07, "outTangent": -2.0000000233721948e-07}]}}}}}, {"type": "BLOCKMAN3.PSShapeModule", "data": {"DATA": {"ENABLE": false, "RADIUS": {"v": 9.999999747378752e-05, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "ANGLE": 0.0, "LENGTH": 5.0, "RADIUS_THICKNESS": 1.0, "DONUT_THICKNESS": 0.20000000298023224, "BOX_THICKNESS": [0.0, 0.0, 0.0], "ARC": {"v": 360.0, "m": 2, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "POSITION": [0.0, 0.0, 0.0], "ROTATION": [0.0, 0.0, 0.0], "SCALE": [1.0, 1.0, 1.0], "TYPE": 2, "STYLE": 2, "ALIGN_TO_DIRECTION": false, "RANDOMIZE_DIRECTION": 0.0, "SPHERIZE_DIRECTION": 0.0, "RANDOMIZE_POSITION": 0.0}}}, {"type": "BLOCKMAN3.PSSizeByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSSizeModule", "data": {"DATA": {"ENABLE": true, "SEPARATE_AXES": false, "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 0.4883720874786377, "inTangent": 1.1038789749145508, "outTangent": 1.1038789749145508}, {"time": 0.7923784852027893, "value": 1.0, "inTangent": -2.0000004496978363e-07, "outTangent": -2.0000004496978363e-07}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSVelocityInheritModule", "data": {"DATA": {"ENABLE": false, "MODE": 0, "MULTIPLIER": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityLimitationModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "SEPARATE_AXES": false, "DRAG_MULTIPLY_BY_SIZE": true, "DRAG_MULTIPLY_BY_VELOCITY": true, "DAMPEN": 0.0, "VELOCITY": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DRAG": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityModule", "data": {"DATA": {"ENABLE": true, "SPACE": 0, "LINEAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Y": {"MAX_SCALAR": -0.20000000298023224, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RADIAL_VELOCITY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED_MODIFIER": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInvokeState", "data": {"START_DELAY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DELAY_ALL_LOOP": false, "DURATION": 5.0, "SIMULATION_SPEED": 1.0, "RANDOM_SEED": 1110503912, "AUTO_RANDOM_SEED": true, "LOOPING": true, "PLAY_ON_AWAKE": true, "USE_SCALED_TIME": true, "CULLING_MODE": 3, "SIMULATION_SPACE": 0, "SIMULATION_SPACE_CUSTOM": {"NULL": {"ref_type": 0}}, "EMITTER_VELOCITY_MODE": 1, "EMITTER_VELOCITY_CUSTOM": [0.0, 0.0, 0.0], "SCALING_MODE": 1}}, {"type": "BLOCKMAN3.PSRenderContext", "data": {"ENABLE": true, "STATE": {"RENDER_MODE": 2, "RENDER_SPACE": 3, "SORT_MODE": 0, "MAX_PARTICLE_SIZE": 0.5, "MIN_PARTICLE_SIZE": 0.0, "CAMERA_SCALE": 0.0, "VELOCITY_SCALE": 0.0, "LENGTH_SCALE": 2.0, "NORMAL_DIRECTION": 1.0, "PIVOT": [0.0, 0.0, 0.0], "FLIP": [0.0, 0.0, 0.0], "ALLOW_ROLL": true}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "3", "enable": true, "inst_id": 21924, "file_id": 7, "layer": 0, "t": [1.0014145374298096, 0.4834517240524292, 1.1582026481628418], "r": [0.7071067094802856, 0.0, 0.0, 0.7071068286895752], "s": [1.0000001192092896, 0.9999999403953552, 0.9999999403953552], "flags": 0, "is_static": false, "component_count": 1, "child_count": 0}}, {"type": "BLOCKMAN3.ParticleSystem", "data": {"materials": [{"exist": true, "sig": {"type": 2, "ptype": 0}, "shared": false, "data": {"head": {"_name": "1", "_type": 2}, "relys": ["9B79E9C70BEC432C4E80728249DEEDD1"], "shader": "Engine/Default_Particle_Blend_one_oneMinusSrcAlpha", "floats": [{"name": "_AlphaThreadHold", "value": 0.5}, {"name": "_EmissiveStrength", "value": 1.0}, {"name": "_Metallic", "value": 0.0}, {"name": "_NormalStrength", "value": 1.0}, {"name": "_Smoothness", "value": 0.5}], "vectors": [{"name": "_EmissiveColor", "value": [0.0, 0.0, 0.0, 1.0]}, {"name": "_EmissiveTex_ST", "value": [1.0, 1.0, 0.0, 0.0]}, {"name": "_NormalTex_ST", "value": [1.0, 1.0, 0.0, 0.0]}], "textures": [{"name": "_MainTex", "value": {"type": 5, "ptype": 2, "id": "9B79E9C70BEC432C4E80728249DEEDD1"}}], "origin_material": {"type": 2, "ptype": 2, "id": "788C3A0D330E46E3A699BB48D311E6CD"}, "name": "1"}}], "shadow_casting_mode": 1, "receive_shadows": true, "estimate_pixel_cull_type": 0, "custom_estimate_pixel_cull_value": 0.0, "inst_id": 21925, "file_id": 8, "enabled": true}}, {"type": "BLOCKMAN3.PSInvokeContext", "data": {"UVModule": {"Data": {"m_Enable": true, "m_Mode": 0, "m_TimeMode": 2, "m_FPS": 18.0, "m_SpeedRange": [0.0, 0.0], "m_TileCountX": 4, "m_TileCountY": 4, "m_AnimationType": 0, "m_RowIndex": 0, "m_CycleCount": 100.0, "m_UVChannelMask": -1, "m_RowMode": 1, "m_FrameOverTime": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 0.0, "inTangent": 1.0, "outTangent": 1.0}, {"time": 1.0, "value": 1.0, "inTangent": 1.0, "outTangent": 1.0}]}}, "m_StartFrame": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_SpriteSequence": {"exist": false}}}}}, {"type": "BLOCKMAN3.PSColorModule", "data": {"DATA": {"ENABLE": true, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 2, "MAX_GRADIENT": {"mode": 0, "alpha": [{"time": 0.05000000074505806, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 0.8091346621513367, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 0.0, "inTangent": 0.0, "outTangent": 0.0}], "color": [[1.0, 1.0, 1.0, 0.0], [1.0, 1.0, 1.0, 1.0]]}}}}}, {"type": "BLOCKMAN3.PSColorByVelocityModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SPEED_RANGE": [0.0, 1.0]}}}, {"type": "BLOCKMAN3.PSEmissionModule", "data": {"DATA": {"ENABLE": true, "RATE_OVER_TIME": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 10.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RATE_OVER_DISTANCE": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "BURSTS": [{"TIME": 0.0, "COUNT": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 30.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "CYCLE": 0, "INTERVAL": 1.2000000476837158, "PROBABILITY": 1.0}]}}}, {"type": "BLOCKMAN3.PSForceModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "RANDOMIZE": false, "FORCE_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInitialModule", "data": {"DATA": {"LIFETIME": {"MAX_SCALAR": 3.0, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "COLOR": {"MAX_COLOR": [0.8745098114013672, 0.3529411852359772, 0.9215686321258545, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SIZE_X": {"MAX_SCALAR": 1.2999999523162842, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FLIP_ROTATION": 0.0, "MAX_PARTICLES": 1000, "USE_3D_SIZE": false, "USE_3D_ROTATION": false}}}, {"type": "BLOCKMAN3.PSRotationByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSRotationModule", "data": {"DATA": {"ENABLE": true, "SEPARATE_AXES": true, "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": -0.48172757029533386, "inTangent": -2.0000000233721948e-07, "outTangent": -2.0000000233721948e-07}, {"time": 0.19973719120025635, "value": 0.5182723999023438, "inTangent": 0.0, "outTangent": 0.0}, {"time": 0.3994743525981903, "value": -0.5049834251403809, "inTangent": 0.0, "outTangent": 0.0}, {"time": 0.6005255579948425, "value": 0.5049834251403809, "inTangent": 0.0, "outTangent": 0.0}, {"time": 0.8015768527984619, "value": -0.5116279125213623, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 0.4883720874786377, "inTangent": -2.0000000233721948e-07, "outTangent": -2.0000000233721948e-07}]}}}}}, {"type": "BLOCKMAN3.PSShapeModule", "data": {"DATA": {"ENABLE": false, "RADIUS": {"v": 9.999999747378752e-05, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "ANGLE": 0.0, "LENGTH": 5.0, "RADIUS_THICKNESS": 1.0, "DONUT_THICKNESS": 0.20000000298023224, "BOX_THICKNESS": [0.0, 0.0, 0.0], "ARC": {"v": 360.0, "m": 2, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "POSITION": [0.0, 0.0, 0.0], "ROTATION": [0.0, 0.0, 0.0], "SCALE": [1.0, 1.0, 1.0], "TYPE": 2, "STYLE": 2, "ALIGN_TO_DIRECTION": false, "RANDOMIZE_DIRECTION": 0.0, "SPHERIZE_DIRECTION": 0.0, "RANDOMIZE_POSITION": 0.0}}}, {"type": "BLOCKMAN3.PSSizeByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSSizeModule", "data": {"DATA": {"ENABLE": true, "SEPARATE_AXES": false, "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 0.4883720874786377, "inTangent": 1.1038789749145508, "outTangent": 1.1038789749145508}, {"time": 0.7923784852027893, "value": 1.0, "inTangent": -2.0000004496978363e-07, "outTangent": -2.0000004496978363e-07}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSVelocityInheritModule", "data": {"DATA": {"ENABLE": false, "MODE": 0, "MULTIPLIER": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityLimitationModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "SEPARATE_AXES": false, "DRAG_MULTIPLY_BY_SIZE": true, "DRAG_MULTIPLY_BY_VELOCITY": true, "DAMPEN": 0.0, "VELOCITY": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DRAG": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityModule", "data": {"DATA": {"ENABLE": true, "SPACE": 0, "LINEAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Y": {"MAX_SCALAR": -0.20000000298023224, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RADIAL_VELOCITY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED_MODIFIER": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInvokeState", "data": {"START_DELAY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DELAY_ALL_LOOP": false, "DURATION": 5.0, "SIMULATION_SPEED": 1.0, "RANDOM_SEED": 82173196, "AUTO_RANDOM_SEED": true, "LOOPING": true, "PLAY_ON_AWAKE": true, "USE_SCALED_TIME": true, "CULLING_MODE": 3, "SIMULATION_SPACE": 0, "SIMULATION_SPACE_CUSTOM": {"NULL": {"ref_type": 0}}, "EMITTER_VELOCITY_MODE": 1, "EMITTER_VELOCITY_CUSTOM": [0.0, 0.0, 0.0], "SCALING_MODE": 1}}, {"type": "BLOCKMAN3.PSRenderContext", "data": {"ENABLE": true, "STATE": {"RENDER_MODE": 2, "RENDER_SPACE": 3, "SORT_MODE": 0, "MAX_PARTICLE_SIZE": 0.5, "MIN_PARTICLE_SIZE": 0.0, "CAMERA_SCALE": 0.0, "VELOCITY_SCALE": 0.0, "LENGTH_SCALE": 2.0, "NORMAL_DIRECTION": 1.0, "PIVOT": [0.0, 0.0, 0.0], "FLIP": [0.0, 0.0, 0.0], "ALLOW_ROLL": true}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "4", "enable": true, "inst_id": 21926, "file_id": 9, "layer": 0, "t": [-1.17974853515625, 0.4724523425102234, -1.142045497894287], "r": [0.7071067094802856, 0.0, 0.0, 0.7071068286895752], "s": [1.0000001192092896, 1.0, 1.0], "flags": 0, "is_static": false, "component_count": 1, "child_count": 0}}, {"type": "BLOCKMAN3.ParticleSystem", "data": {"materials": [{"exist": true, "sig": {"type": 2, "ptype": 0}, "shared": false, "data": {"head": {"_name": "1", "_type": 2}, "relys": ["9B79E9C70BEC432C4E80728249DEEDD1"], "shader": "Engine/Default_Particle_Blend_one_oneMinusSrcAlpha", "floats": [{"name": "_AlphaThreadHold", "value": 0.5}, {"name": "_EmissiveStrength", "value": 1.0}, {"name": "_Metallic", "value": 0.0}, {"name": "_NormalStrength", "value": 1.0}, {"name": "_Smoothness", "value": 0.5}], "vectors": [{"name": "_EmissiveColor", "value": [0.0, 0.0, 0.0, 1.0]}, {"name": "_EmissiveTex_ST", "value": [1.0, 1.0, 0.0, 0.0]}, {"name": "_NormalTex_ST", "value": [1.0, 1.0, 0.0, 0.0]}], "textures": [{"name": "_MainTex", "value": {"type": 5, "ptype": 2, "id": "9B79E9C70BEC432C4E80728249DEEDD1"}}], "origin_material": {"type": 2, "ptype": 2, "id": "788C3A0D330E46E3A699BB48D311E6CD"}, "name": "1"}}], "shadow_casting_mode": 1, "receive_shadows": true, "estimate_pixel_cull_type": 0, "custom_estimate_pixel_cull_value": 0.0, "inst_id": 21927, "file_id": 10, "enabled": true}}, {"type": "BLOCKMAN3.PSInvokeContext", "data": {"UVModule": {"Data": {"m_Enable": true, "m_Mode": 0, "m_TimeMode": 2, "m_FPS": 18.0, "m_SpeedRange": [0.0, 0.0], "m_TileCountX": 4, "m_TileCountY": 4, "m_AnimationType": 0, "m_RowIndex": 0, "m_CycleCount": 100.0, "m_UVChannelMask": -1, "m_RowMode": 1, "m_FrameOverTime": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 0.0, "inTangent": 1.0, "outTangent": 1.0}, {"time": 1.0, "value": 1.0, "inTangent": 1.0, "outTangent": 1.0}]}}, "m_StartFrame": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_SpriteSequence": {"exist": false}}}}}, {"type": "BLOCKMAN3.PSColorModule", "data": {"DATA": {"ENABLE": true, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 2, "MAX_GRADIENT": {"mode": 0, "alpha": [{"time": 0.05000000074505806, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 0.8091346621513367, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 0.0, "inTangent": 0.0, "outTangent": 0.0}], "color": [[1.0, 1.0, 1.0, 0.0], [1.0, 1.0, 1.0, 1.0]]}}}}}, {"type": "BLOCKMAN3.PSColorByVelocityModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SPEED_RANGE": [0.0, 1.0]}}}, {"type": "BLOCKMAN3.PSEmissionModule", "data": {"DATA": {"ENABLE": true, "RATE_OVER_TIME": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 10.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RATE_OVER_DISTANCE": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "BURSTS": [{"TIME": 0.0, "COUNT": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 30.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "CYCLE": 0, "INTERVAL": 1.2000000476837158, "PROBABILITY": 1.0}]}}}, {"type": "BLOCKMAN3.PSForceModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "RANDOMIZE": false, "FORCE_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInitialModule", "data": {"DATA": {"LIFETIME": {"MAX_SCALAR": 3.0, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "COLOR": {"MAX_COLOR": [0.8745098114013672, 0.3529411852359772, 0.9215686321258545, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SIZE_X": {"MAX_SCALAR": 1.2999999523162842, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FLIP_ROTATION": 0.0, "MAX_PARTICLES": 1000, "USE_3D_SIZE": false, "USE_3D_ROTATION": false}}}, {"type": "BLOCKMAN3.PSRotationByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSRotationModule", "data": {"DATA": {"ENABLE": true, "SEPARATE_AXES": true, "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": -0.48172757029533386, "inTangent": -2.0000000233721948e-07, "outTangent": -2.0000000233721948e-07}, {"time": 0.19973719120025635, "value": 0.5182723999023438, "inTangent": 0.0, "outTangent": 0.0}, {"time": 0.3994743525981903, "value": -0.5049834251403809, "inTangent": 0.0, "outTangent": 0.0}, {"time": 0.6005255579948425, "value": 0.5049834251403809, "inTangent": 0.0, "outTangent": 0.0}, {"time": 0.8015768527984619, "value": -0.5116279125213623, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 0.4883720874786377, "inTangent": -2.0000000233721948e-07, "outTangent": -2.0000000233721948e-07}]}}}}}, {"type": "BLOCKMAN3.PSShapeModule", "data": {"DATA": {"ENABLE": false, "RADIUS": {"v": 9.999999747378752e-05, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "ANGLE": 0.0, "LENGTH": 5.0, "RADIUS_THICKNESS": 1.0, "DONUT_THICKNESS": 0.20000000298023224, "BOX_THICKNESS": [0.0, 0.0, 0.0], "ARC": {"v": 360.0, "m": 2, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "POSITION": [0.0, 0.0, 0.0], "ROTATION": [0.0, 0.0, 0.0], "SCALE": [1.0, 1.0, 1.0], "TYPE": 2, "STYLE": 2, "ALIGN_TO_DIRECTION": false, "RANDOMIZE_DIRECTION": 0.0, "SPHERIZE_DIRECTION": 0.0, "RANDOMIZE_POSITION": 0.0}}}, {"type": "BLOCKMAN3.PSSizeByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSSizeModule", "data": {"DATA": {"ENABLE": true, "SEPARATE_AXES": false, "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 0.4883720874786377, "inTangent": 1.1038789749145508, "outTangent": 1.1038789749145508}, {"time": 0.7923784852027893, "value": 1.0, "inTangent": -2.0000004496978363e-07, "outTangent": -2.0000004496978363e-07}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSVelocityInheritModule", "data": {"DATA": {"ENABLE": false, "MODE": 0, "MULTIPLIER": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityLimitationModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "SEPARATE_AXES": false, "DRAG_MULTIPLY_BY_SIZE": true, "DRAG_MULTIPLY_BY_VELOCITY": true, "DAMPEN": 0.0, "VELOCITY": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DRAG": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityModule", "data": {"DATA": {"ENABLE": true, "SPACE": 0, "LINEAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Y": {"MAX_SCALAR": -0.20000000298023224, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RADIAL_VELOCITY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED_MODIFIER": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInvokeState", "data": {"START_DELAY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DELAY_ALL_LOOP": false, "DURATION": 5.0, "SIMULATION_SPEED": 1.0, "RANDOM_SEED": 1000522215, "AUTO_RANDOM_SEED": true, "LOOPING": true, "PLAY_ON_AWAKE": true, "USE_SCALED_TIME": true, "CULLING_MODE": 3, "SIMULATION_SPACE": 0, "SIMULATION_SPACE_CUSTOM": {"NULL": {"ref_type": 0}}, "EMITTER_VELOCITY_MODE": 1, "EMITTER_VELOCITY_CUSTOM": [0.0, 0.0, 0.0], "SCALING_MODE": 1}}, {"type": "BLOCKMAN3.PSRenderContext", "data": {"ENABLE": true, "STATE": {"RENDER_MODE": 2, "RENDER_SPACE": 3, "SORT_MODE": 0, "MAX_PARTICLE_SIZE": 0.5, "MIN_PARTICLE_SIZE": 0.0, "CAMERA_SCALE": 0.0, "VELOCITY_SCALE": 0.0, "LENGTH_SCALE": 2.0, "NORMAL_DIRECTION": 1.0, "PIVOT": [0.0, 0.0, 0.0], "FLIP": [0.0, 0.0, 0.0], "ALLOW_ROLL": true}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "5", "enable": true, "inst_id": 21928, "file_id": 11, "layer": 0, "t": [0.0, -0.012143399566411972, 1.3969965095839143e-07], "r": [-0.7071067094802856, 0.0, 0.0, 0.7071068286895752], "s": [1.2100000381469727, 1.2100000381469727, 1.0], "flags": 0, "is_static": false, "component_count": 1, "child_count": 0}}, {"type": "BLOCKMAN3.ParticleSystem", "data": {"materials": [{"exist": true, "sig": {"type": 2, "ptype": 0}, "shared": false, "data": {"head": {"_name": "5", "_type": 2}, "relys": ["E04F22A4944946B751AC7DDEC3D6A9A4"], "shader": "Engine/Default_Particle_Blend_one_oneMinusSrcAlpha", "floats": [{"name": "_AlphaThreadHold", "value": 0.5}, {"name": "_EmissiveStrength", "value": 1.0}, {"name": "_Metallic", "value": 0.0}, {"name": "_NormalStrength", "value": 1.0}, {"name": "_Smoothness", "value": 0.5}], "vectors": [{"name": "_EmissiveColor", "value": [0.0, 0.0, 0.0, 1.0]}, {"name": "_EmissiveTex_ST", "value": [1.0, 1.0, 0.0, 0.0]}, {"name": "_NormalTex_ST", "value": [1.0, 1.0, 0.0, 0.0]}], "textures": [{"name": "_MainTex", "value": {"type": 5, "ptype": 2, "id": "E04F22A4944946B751AC7DDEC3D6A9A4"}}], "origin_material": {"type": 2, "ptype": 2, "id": "3171EB0F90064F4F9291C18C50ED3F12"}, "name": "5"}}], "shadow_casting_mode": 1, "receive_shadows": true, "estimate_pixel_cull_type": 0, "custom_estimate_pixel_cull_value": 0.0, "inst_id": 21929, "file_id": 12, "enabled": true}}, {"type": "BLOCKMAN3.PSInvokeContext", "data": {"UVModule": {"Data": {"m_Enable": true, "m_Mode": 0, "m_TimeMode": 2, "m_FPS": 15.0, "m_SpeedRange": [0.0, 0.0], "m_TileCountX": 3, "m_TileCountY": 4, "m_AnimationType": 0, "m_RowIndex": 0, "m_CycleCount": 100.0, "m_UVChannelMask": -1, "m_RowMode": 1, "m_FrameOverTime": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 0.0, "inTangent": 1.0, "outTangent": 1.0}, {"time": 1.0, "value": 1.0, "inTangent": 1.0, "outTangent": 1.0}]}}, "m_StartFrame": {"MAX_SCALAR": 6.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1}, "m_SpriteSequence": {"exist": false}}}}}, {"type": "BLOCKMAN3.PSColorModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}}}}, {"type": "BLOCKMAN3.PSColorByVelocityModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SPEED_RANGE": [0.0, 1.0]}}}, {"type": "BLOCKMAN3.PSEmissionModule", "data": {"DATA": {"ENABLE": true, "RATE_OVER_TIME": {"MAX_SCALAR": 50.0, "MIN_SCALAR": 10.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RATE_OVER_DISTANCE": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "BURSTS": []}}}, {"type": "BLOCKMAN3.PSForceModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "RANDOMIZE": false, "FORCE_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInitialModule", "data": {"DATA": {"LIFETIME": {"MAX_SCALAR": 0.30000001192092896, "MIN_SCALAR": 0.15000000596046448, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1}, "SPEED": {"MAX_SCALAR": 0.009999999776482582, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "COLOR": {"MAX_COLOR": [0.8745098114013672, 0.3529411852359772, 0.9215686321258545, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FLIP_ROTATION": 0.0, "MAX_PARTICLES": 1000, "USE_3D_SIZE": false, "USE_3D_ROTATION": false}}}, {"type": "BLOCKMAN3.PSRotationByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSRotationModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSShapeModule", "data": {"DATA": {"ENABLE": true, "RADIUS": {"v": 1.0, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "ANGLE": 25.0, "LENGTH": 5.0, "RADIUS_THICKNESS": 1.0, "DONUT_THICKNESS": 0.20000000298023224, "BOX_THICKNESS": [0.0, 0.0, 0.0], "ARC": {"v": 360.0, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "POSITION": [0.0, 0.0, 0.0], "ROTATION": [0.0, 0.0, 0.0], "SCALE": [2.5, 2.5, 0.20000000298023224], "TYPE": 4, "STYLE": 0, "ALIGN_TO_DIRECTION": false, "RANDOMIZE_DIRECTION": 0.0, "SPHERIZE_DIRECTION": 0.0, "RANDOMIZE_POSITION": 0.0}}}, {"type": "BLOCKMAN3.PSSizeByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSSizeModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSVelocityInheritModule", "data": {"DATA": {"ENABLE": false, "MODE": 0, "MULTIPLIER": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityLimitationModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "SEPARATE_AXES": false, "DRAG_MULTIPLY_BY_SIZE": true, "DRAG_MULTIPLY_BY_VELOCITY": true, "DAMPEN": 0.0, "VELOCITY": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DRAG": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "LINEAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RADIAL_VELOCITY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED_MODIFIER": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInvokeState", "data": {"START_DELAY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DELAY_ALL_LOOP": false, "DURATION": 10.0, "SIMULATION_SPEED": 1.0, "RANDOM_SEED": 734049700, "AUTO_RANDOM_SEED": true, "LOOPING": true, "PLAY_ON_AWAKE": true, "USE_SCALED_TIME": true, "CULLING_MODE": 3, "SIMULATION_SPACE": 0, "SIMULATION_SPACE_CUSTOM": {"NULL": {"ref_type": 0}}, "EMITTER_VELOCITY_MODE": 1, "EMITTER_VELOCITY_CUSTOM": [0.0, 0.0, 0.0], "SCALING_MODE": 1}}, {"type": "BLOCKMAN3.PSRenderContext", "data": {"ENABLE": true, "STATE": {"RENDER_MODE": 0, "RENDER_SPACE": 3, "SORT_MODE": 0, "MAX_PARTICLE_SIZE": 0.5, "MIN_PARTICLE_SIZE": 0.0, "CAMERA_SCALE": 0.0, "VELOCITY_SCALE": 0.0, "LENGTH_SCALE": 2.0, "NORMAL_DIRECTION": 1.0, "PIVOT": [0.0, 0.0, 0.0], "FLIP": [0.0, 0.0, 0.0], "ALLOW_ROLL": true}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "6", "enable": true, "inst_id": 21930, "file_id": 13, "layer": 0, "t": [0.0, 0.054512422531843185, 0.0], "r": [-0.7071067690849304, 0.0, 0.0, 0.7071067690849304], "s": [1.0, 0.9999998807907104, 0.9999998807907104], "flags": 0, "is_static": false, "component_count": 1, "child_count": 0}}, {"type": "BLOCKMAN3.ParticleSystem", "data": {"materials": [{"exist": true, "sig": {"type": 2, "ptype": 0}, "shared": false, "data": {"head": {"_name": "6", "_type": 2}, "relys": ["077EBD76E76C4916A7A5BCBD4BD2423A"], "shader": "Engine/Default_Particle_Blend_one_oneMinusSrcAlpha", "floats": [{"name": "_AlphaThreadHold", "value": 0.5}, {"name": "_EmissiveStrength", "value": 1.0}, {"name": "_Metallic", "value": 0.0}, {"name": "_NormalStrength", "value": 1.0}, {"name": "_Smoothness", "value": 0.5}], "vectors": [{"name": "_EmissiveColor", "value": [0.0, 0.0, 0.0, 1.0]}, {"name": "_EmissiveTex_ST", "value": [1.0, 1.0, 0.0, 0.0]}, {"name": "_NormalTex_ST", "value": [1.0, 1.0, 0.0, 0.0]}], "textures": [{"name": "_MainTex", "value": {"type": 5, "ptype": 2, "id": "077EBD76E76C4916A7A5BCBD4BD2423A"}}], "origin_material": {"type": 2, "ptype": 2, "id": "3AD329E4E6EA4E6AD6AD5BCB25F4B215"}, "name": "6"}}], "shadow_casting_mode": 1, "receive_shadows": true, "estimate_pixel_cull_type": 0, "custom_estimate_pixel_cull_value": 0.0, "inst_id": 21931, "file_id": 14, "enabled": true}}, {"type": "BLOCKMAN3.PSInvokeContext", "data": {"UVModule": {"Data": {"m_Enable": true, "m_Mode": 0, "m_TimeMode": 0, "m_FPS": 30.0, "m_SpeedRange": [0.0, 0.0], "m_TileCountX": 3, "m_TileCountY": 3, "m_AnimationType": 0, "m_RowIndex": 0, "m_CycleCount": 1.0, "m_UVChannelMask": -1, "m_RowMode": 1, "m_FrameOverTime": {"MAX_SCALAR": 3.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 0.0, "inTangent": 1.3874484300613403, "outTangent": 1.3874484300613403}, {"time": 1.0, "value": 1.0, "inTangent": 0.6427726745605469, "outTangent": 0.6427726745605469}]}}, "m_StartFrame": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_SpriteSequence": {"exist": false}}}}}, {"type": "BLOCKMAN3.PSColorModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 2, "MAX_GRADIENT": {"mode": 0, "alpha": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 0.8990384936332703, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 0.0, "inTangent": 0.0, "outTangent": 0.0}], "color": [[1.0, 1.0, 1.0, 0.0], [1.0, 1.0, 1.0, 1.0]]}}}}}, {"type": "BLOCKMAN3.PSColorByVelocityModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SPEED_RANGE": [0.0, 1.0]}}}, {"type": "BLOCKMAN3.PSEmissionModule", "data": {"DATA": {"ENABLE": true, "RATE_OVER_TIME": {"MAX_SCALAR": 15.0, "MIN_SCALAR": 10.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RATE_OVER_DISTANCE": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "BURSTS": []}}}, {"type": "BLOCKMAN3.PSForceModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "RANDOMIZE": false, "FORCE_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInitialModule", "data": {"DATA": {"LIFETIME": {"MAX_SCALAR": 4.0, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "COLOR": {"MAX_COLOR": [0.8745098114013672, 0.3529411852359772, 0.9215686321258545, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SIZE_X": {"MAX_SCALAR": 1.5, "MIN_SCALAR": 0.5, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 0.049833886325359344, "inTangent": 0.0, "outTangent": 0.0}]}, "MIN_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}, "MIN_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}, "MIN_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "ROTATION_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1}, "ROTATION_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1}, "ROTATION_Z": {"MAX_SCALAR": 360.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1}, "FLIP_ROTATION": 0.0, "MAX_PARTICLES": 1000, "USE_3D_SIZE": false, "USE_3D_ROTATION": false}}}, {"type": "BLOCKMAN3.PSRotationByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSRotationModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSShapeModule", "data": {"DATA": {"ENABLE": true, "RADIUS": {"v": 1.0, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "ANGLE": 25.0, "LENGTH": 5.0, "RADIUS_THICKNESS": 0.0, "DONUT_THICKNESS": 9.999999747378752e-05, "BOX_THICKNESS": [0.0, 0.0, 0.0], "ARC": {"v": 360.0, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "POSITION": [0.0, 0.0, 0.0], "ROTATION": [0.0, 0.0, 0.0], "SCALE": [1.0, 1.0, 1.2999999523162842], "TYPE": 3, "STYLE": 2, "ALIGN_TO_DIRECTION": false, "RANDOMIZE_DIRECTION": 0.0, "SPHERIZE_DIRECTION": 0.0, "RANDOMIZE_POSITION": 0.0}}}, {"type": "BLOCKMAN3.PSSizeByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSSizeModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSVelocityInheritModule", "data": {"DATA": {"ENABLE": false, "MODE": 0, "MULTIPLIER": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityLimitationModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "SEPARATE_AXES": false, "DRAG_MULTIPLY_BY_SIZE": true, "DRAG_MULTIPLY_BY_VELOCITY": true, "DAMPEN": 0.0, "VELOCITY": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DRAG": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "LINEAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RADIAL_VELOCITY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED_MODIFIER": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInvokeState", "data": {"START_DELAY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DELAY_ALL_LOOP": false, "DURATION": 1.0, "SIMULATION_SPEED": 0.5, "RANDOM_SEED": 934370929, "AUTO_RANDOM_SEED": false, "LOOPING": true, "PLAY_ON_AWAKE": true, "USE_SCALED_TIME": true, "CULLING_MODE": 3, "SIMULATION_SPACE": 0, "SIMULATION_SPACE_CUSTOM": {"NULL": {"ref_type": 0}}, "EMITTER_VELOCITY_MODE": 1, "EMITTER_VELOCITY_CUSTOM": [0.0, 0.0, 0.0], "SCALING_MODE": 1}}, {"type": "BLOCKMAN3.PSRenderContext", "data": {"ENABLE": true, "STATE": {"RENDER_MODE": 0, "RENDER_SPACE": 3, "SORT_MODE": 0, "MAX_PARTICLE_SIZE": 0.5, "MIN_PARTICLE_SIZE": 0.0, "CAMERA_SCALE": 0.0, "VELOCITY_SCALE": 0.0, "LENGTH_SCALE": 2.0, "NORMAL_DIRECTION": 1.0, "PIVOT": [0.0, 0.0, 0.0], "FLIP": [0.0, 0.0, 0.0], "ALLOW_ROLL": true}}}]}, "head": {"_name": "", "_type": 1}, "relys": ["9B79E9C70BEC432C4E80728249DEEDD1", "86F49EBA64FE44ED96DBB7DE9440C25F", "E04F22A4944946B751AC7DDEC3D6A9A4", "788C3A0D330E46E3A699BB48D311E6CD", "00903E6839EF478E31A7BE018CBC1463", "077EBD76E76C4916A7A5BCBD4BD2423A", "3AD329E4E6EA4E6AD6AD5BCB25F4B215", "3171EB0F90064F4F9291C18C50ED3F12"]}