#pragma name "Game/EmissionLight"
#pragma vertex vert
#pragma fragment frag
#pragma property texture2D _MainTex
// #pragma property vector _MainTex_ST (1,1,0,0)
#pragma property color _Emission (1)
#pragma using MatrixMVP

#pragma shader_feature_fog

struct vrt {
    float4 pos : POSITION;
    half2 uv : TEXCOORD0;
};

struct v2f {
    float4 pos : SV_POSITION;
    half2 uv : TEXCOORD0;
}; 

v2f vert(vrt i) {
    v2f v;
    v.pos = ObjectToClipPos(i.pos);
    v.uv = i.uv;
    return v;
}

half4 frag(v2f f) {
    float4 col = float4(_Emission.rgb,0.) * tex2D(_MainTex,f.uv);

    ApplyFog(col);
    return col;
}