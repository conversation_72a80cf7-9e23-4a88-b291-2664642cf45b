{"prefab": {"block_count": 5, "file_map_identity": 6, "objects": [{"type": "BLOCKMAN3.GameObject", "data": {"name": "1_cubeRoad", "enable": true, "inst_id": 6761, "file_id": 1, "layer": 0, "t": [0, 0, 0], "r": [0, 0, 0, 1], "s": [1, 1, 1], "flags": 0, "is_static": false, "component_count": 0, "child_count": 4}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "1_cubeBlockWithHole_01", "enable": true, "inst_id": 6946, "file_id": 2, "layer": 0, "t": [1.0229943990707397, -5.92242431640625, 0.6150903701782227], "r": [0, 0, 0, 1], "s": [1, 1, 1], "flags": 2, "is_static": false, "prefab": {"container": {"exist": true, "sig": {"type": 1, "ptype": 2, "id": "E778A843B113433E9E8E436E5F93DA6E"}, "shared": true}, "state": 1}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "1_cubeBlockWithHole_03", "enable": true, "inst_id": 6961, "file_id": 3, "layer": 0, "t": [1.0229943990707397, -5.92242431640625, -11.977951049804688], "r": [0, 0, 0, 1], "s": [1, 1, 1], "flags": 2, "is_static": false, "prefab": {"container": {"exist": true, "sig": {"type": 1, "ptype": 2, "id": "E778A843B113433E9E8E436E5F93DA6E"}, "shared": true}, "state": 1}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "1_cubeBlockWithHole_04", "enable": true, "inst_id": 6976, "file_id": 4, "layer": 0, "t": [1.0229943990707397, -5.92242431640625, 25.702552795410156], "r": [0, 0, 0, 1], "s": [1, 1, 1], "flags": 2, "is_static": false, "prefab": {"container": {"exist": true, "sig": {"type": 1, "ptype": 2, "id": "E778A843B113433E9E8E436E5F93DA6E"}, "shared": true}, "state": 1}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "1_cubeBlockWithHole_05", "enable": true, "inst_id": 6991, "file_id": 5, "layer": 0, "t": [1.0229943990707397, -5.92242431640625, 13.109514236450195], "r": [0, 0, 0, 1], "s": [1, 1, 1], "flags": 2, "is_static": false, "prefab": {"container": {"exist": true, "sig": {"type": 1, "ptype": 2, "id": "E778A843B113433E9E8E436E5F93DA6E"}, "shared": true}, "state": 1}}}]}, "head": {"_name": "", "_type": 1}, "relys": ["E778A843B113433E9E8E436E5F93DA6E"]}