{"prefab": {"block_count": 9, "file_map_identity": 16, "objects": [{"type": "BLOCKMAN3.GameObject", "data": {"name": "2_harmDecideRoad", "enable": true, "inst_id": 32483, "file_id": 1, "layer": 0, "t": [0, 0, 0], "r": [0, 0, 0, 1], "s": [1, 1, 1], "flags": 0, "is_static": false, "component_count": 0, "child_count": 8}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "1_harmcubeBlockWithHole", "enable": true, "inst_id": 32484, "file_id": 2, "layer": 0, "t": [0.4406052231788635, 3.5686559677124023, -6.119950294494629], "r": [0.7071068286895752, 0, 0, 0.7071068286895752], "s": [1, 1, 1], "flags": 2, "is_static": false, "prefab": {"container": {"exist": true, "sig": {"type": 1, "ptype": 2, "id": "3E72BC4FC8C548C8A386E85A7DF24EAA"}, "shared": true}, "state": 1}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "1_harmtriangleBlockWithHole", "enable": true, "inst_id": 32503, "file_id": 3, "layer": 0, "t": [-1.8259342908859253, 4.457888126373291, 0.09871292114257812], "r": [-0.6830127239227295, 0.18301278352737432, -0.1830126047134399, 0.6830126643180847], "s": [1, 1, 1], "flags": 2, "is_static": false, "prefab": {"container": {"exist": true, "sig": {"type": 1, "ptype": 2, "id": "4E3F8F97FE7A49563E86D802FC6323C2"}, "shared": true}, "state": 1}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "1_decide", "enable": true, "inst_id": 32518, "file_id": 4, "layer": 0, "t": [-3.189383029937744, 3.512770652770996, 6.576963424682617], "r": [0, 0, 0, 1], "s": [1, 1, 1], "flags": 2, "is_static": false, "prefab": {"container": {"exist": true, "sig": {"type": 1, "ptype": 2, "id": "E5017623397C4A5F1091AAB660CCC5A3"}, "shared": true}, "state": 1}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "basicBlock_big", "enable": true, "inst_id": 32563, "file_id": 5, "layer": 0, "t": [-0.0327483266592026, -0.2603626251220703, -5.29677677154541], "r": [0, 0, 0, 1], "s": [5, 0.300000011920929, 12], "flags": 2, "is_static": false, "prefab": {"container": {"exist": true, "sig": {"type": 1, "ptype": 2, "id": "7F13A82528BB414882977A359BC87322"}, "shared": true}, "state": 1}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "basicBlock_big_01", "enable": true, "inst_id": 32566, "file_id": 8, "layer": 0, "t": [-0.0327483266592026, -0.2603626251220703, 6.7095746994018555], "r": [0, 0, 0, 1], "s": [13.640000343322754, 0.300000011920929, 12], "flags": 2, "is_static": false, "prefab": {"container": {"exist": true, "sig": {"type": 1, "ptype": 2, "id": "7F13A82528BB414882977A359BC87322"}, "shared": true}, "state": 1}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "1_harmcubeBlockWithHole_01", "enable": true, "inst_id": 32569, "file_id": 11, "layer": 0, "t": [0.4406052231788635, 3.5686590671539307, 19.620424270629883], "r": [-0.7071068286895752, 0, 0, -0.7071068286895752], "s": [1, 1, 1], "flags": 2, "is_static": false, "prefab": {"container": {"exist": true, "sig": {"type": 1, "ptype": 2, "id": "3E72BC4FC8C548C8A386E85A7DF24EAA"}, "shared": true}, "state": 1}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "1_harmtriangleBlockWithHole_01", "enable": true, "inst_id": 32588, "file_id": 12, "layer": 0, "t": [-1.8259309530258179, 4.457886219024658, 13.360960006713867], "r": [0.6830127239227295, -0.18301276862621313, 0.1830125749111176, -0.6830127239227295], "s": [1, 1, 1], "flags": 2, "is_static": false, "prefab": {"container": {"exist": true, "sig": {"type": 1, "ptype": 2, "id": "4E3F8F97FE7A49563E86D802FC6323C2"}, "shared": true}, "state": 1}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "basicBlock_big_02", "enable": true, "inst_id": 32603, "file_id": 13, "layer": 0, "t": [-0.032744105905294404, -0.2603626251220703, 18.677902221679688], "r": [0, -8.742277657347586e-08, 0, -1], "s": [5, 0.300000011920929, 12], "flags": 2, "is_static": false, "prefab": {"container": {"exist": true, "sig": {"type": 1, "ptype": 2, "id": "7F13A82528BB414882977A359BC87322"}, "shared": true}, "state": 1}}}]}, "head": {"_name": "", "_type": 1}, "relys": ["7F13A82528BB414882977A359BC87322", "3E72BC4FC8C548C8A386E85A7DF24EAA", "4E3F8F97FE7A49563E86D802FC6323C2", "E5017623397C4A5F1091AAB660CCC5A3"]}