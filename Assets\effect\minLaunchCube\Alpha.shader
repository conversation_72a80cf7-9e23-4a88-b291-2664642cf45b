#pragma name "Engine/Default_Particle_Blend_one_oneMinusSrcAlpha"
#pragma vertex vert
#pragma fragment frag
#pragma cull back
#pragma property texture2D _MainTex
#pragma property vector _MainTex_ST (1,1,0,0)

#pragma property color _Color (1)
#pragma using MatrixM, MatrixMVP
#pragma zwrite off
#pragma blendop add
#pragma blend one oneMinusSrcAlpha
#pragma renderqueue 3000

struct vrt {
    float4 pos : POSITION;
    half3 normal : NORMAL;
    half4 color : COLOR;
    half2 uv : TEXCOORD0;
};

struct v2f {
    float4 pos : SV_POSITION;
    half3 worldNormal : NORMAL;
    half4 color : COLOR;
    half2 uv : TEXCOORD0;
}; 

v2f vert(vrt i) {
    v2f v;
    v.pos = ObjectToClipPos(i.pos);
    v.worldNormal = normalize(mat3(MatrixM) * i.normal);
    v.uv = TRANSFORM_TEX(i.uv, _MainTex_ST);
    v.color = i.color;
    return v;
}

half4 frag(v2f f) {
    float4 texCol = tex2D(_MainTex, f.uv);
	vec4 color = texCol * _Color * f.color;
    color.rgb *= color.a;

    return color;
}