{"prefab": {"block_count": 19, "file_map_identity": 4, "objects": [{"type": "BLOCKMAN3.GameObject", "data": {"name": "OradinaryTailing", "enable": true, "inst_id": 139866, "file_id": 1, "layer": 0, "t": [0.0, 7.001402378082275, 0.0], "r": [0.0, 0.0, 0.0, 1.0], "s": [1.0, 1.0, 1.0], "flags": 0, "is_static": false, "component_count": 0, "child_count": 1}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "OradinaryTailing", "enable": true, "inst_id": 139867, "file_id": 2, "layer": 0, "t": [0.0, 0.0, 0.0], "r": [0.0, 1.0, 0.0, -4.371138828673793e-08], "s": [1.0, 1.0, 1.0], "flags": 0, "is_static": false, "component_count": 1, "child_count": 0}}, {"type": "BLOCKMAN3.ParticleSystem", "data": {"materials": [{"exist": true, "sig": {"type": 2, "ptype": 2, "id": "F7A2003F34864F56F7A9DB9E8EB1127A"}, "shared": true}], "shadow_casting_mode": 1, "receive_shadows": true, "estimate_pixel_cull_type": 0, "custom_estimate_pixel_cull_value": 0.0, "inst_id": 139868, "file_id": 3, "enabled": true}}, {"type": "BLOCKMAN3.PSInvokeContext", "data": {"UVModule": {"Data": {"m_Enable": false, "m_Mode": 0, "m_TimeMode": 2, "m_FPS": 24.0, "m_SpeedRange": [0.0, 0.0], "m_TileCountX": 3, "m_TileCountY": 3, "m_AnimationType": 0, "m_RowIndex": 0, "m_CycleCount": 1.0, "m_UVChannelMask": -1, "m_RowMode": 1, "m_FrameOverTime": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": -1.0, "outTangent": -1.0}, {"time": 1.0, "value": 0.0, "inTangent": -1.0, "outTangent": -1.0}]}}, "m_StartFrame": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_SpriteSequence": {"exist": false}}}}}, {"type": "BLOCKMAN3.PSColorModule", "data": {"DATA": {"ENABLE": true, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 2, "MAX_GRADIENT": {"mode": 0, "alpha": [{"time": 0.0, "value": 1.0, "inTangent": 1.401298464324817e-45, "outTangent": 0.0}, {"time": 1.0, "value": 0.0, "inTangent": 1.401298464324817e-45, "outTangent": 0.0}], "color": [[1.0, 1.0, 1.0, 0.0], [1.0, 1.0, 1.0, 1.0]]}}}}}, {"type": "BLOCKMAN3.PSColorByVelocityModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SPEED_RANGE": [0.0, 1.0]}}}, {"type": "BLOCKMAN3.PSEmissionModule", "data": {"DATA": {"ENABLE": true, "RATE_OVER_TIME": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 10.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RATE_OVER_DISTANCE": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "BURSTS": [{"TIME": 0.0, "COUNT": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 30.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "CYCLE": 0, "INTERVAL": 0.05000000074505806, "PROBABILITY": 1.0}]}}}, {"type": "BLOCKMAN3.PSForceModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "RANDOMIZE": false, "FORCE_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInitialModule", "data": {"DATA": {"LIFETIME": {"MAX_SCALAR": 0.10000000149011612, "MIN_SCALAR": 0.20000000298023224, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1}, "SPEED": {"MAX_SCALAR": 5.0, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "COLOR": {"MAX_COLOR": [0.7882353067398071, 0.9843137264251709, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SIZE_X": {"MAX_SCALAR": 0.800000011920929, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Y": {"MAX_SCALAR": 0.30000001192092896, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Z": {"MAX_SCALAR": 1.570796251296997, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FLIP_ROTATION": 0.0, "MAX_PARTICLES": 1000, "USE_3D_SIZE": true, "USE_3D_ROTATION": false}}}, {"type": "BLOCKMAN3.PSRotationByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSRotationModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSShapeModule", "data": {"DATA": {"ENABLE": true, "RADIUS": {"v": 1.0, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "ANGLE": 25.0, "LENGTH": 5.0, "RADIUS_THICKNESS": 1.0, "DONUT_THICKNESS": 0.20000000298023224, "BOX_THICKNESS": [0.0, 0.0, 0.0], "ARC": {"v": 360.0, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "POSITION": [0.0, 0.0, 0.0], "ROTATION": [0.0, 0.0, 0.0], "SCALE": [0.8500000238418579, 1.1100000143051147, 0.5], "TYPE": 4, "STYLE": 2, "ALIGN_TO_DIRECTION": false, "RANDOMIZE_DIRECTION": 0.0, "SPHERIZE_DIRECTION": 0.0, "RANDOMIZE_POSITION": 0.20000000298023224}}}, {"type": "BLOCKMAN3.PSSizeByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSSizeModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSVelocityInheritModule", "data": {"DATA": {"ENABLE": false, "MODE": 0, "MULTIPLIER": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityLimitationModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "SEPARATE_AXES": false, "DRAG_MULTIPLY_BY_SIZE": true, "DRAG_MULTIPLY_BY_VELOCITY": true, "DAMPEN": 0.0, "VELOCITY": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DRAG": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "LINEAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RADIAL_VELOCITY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED_MODIFIER": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInvokeState", "data": {"START_DELAY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DELAY_ALL_LOOP": false, "DURATION": 5.0, "SIMULATION_SPEED": 1.0, "RANDOM_SEED": 646995719, "AUTO_RANDOM_SEED": true, "LOOPING": true, "PLAY_ON_AWAKE": false, "USE_SCALED_TIME": true, "CULLING_MODE": 3, "SIMULATION_SPACE": 0, "SIMULATION_SPACE_CUSTOM": {"NULL": {"ref_type": 0}}, "EMITTER_VELOCITY_MODE": 1, "EMITTER_VELOCITY_CUSTOM": [0.0, 0.0, 0.0], "SCALING_MODE": 1}}, {"type": "BLOCKMAN3.PSRenderContext", "data": {"ENABLE": true, "STATE": {"RENDER_MODE": 1, "RENDER_SPACE": 3, "SORT_MODE": 0, "MAX_PARTICLE_SIZE": 0.5, "MIN_PARTICLE_SIZE": 0.0, "CAMERA_SCALE": 0.0, "VELOCITY_SCALE": 0.0, "LENGTH_SCALE": 2.0, "NORMAL_DIRECTION": 1.0, "PIVOT": [0.0, 0.0, 0.0], "FLIP": [0.0, 0.0, 0.0], "ALLOW_ROLL": true}}}]}, "head": {"_name": "", "_type": 1}, "relys": ["6C0DCDFE6922427EBC94EAF703CA45C6", "F7A2003F34864F56F7A9DB9E8EB1127A"]}