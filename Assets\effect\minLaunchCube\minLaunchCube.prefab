{"prefab": {"block_count": 54, "file_map_identity": 7, "objects": [{"type": "BLOCKMAN3.GameObject", "data": {"name": "minLaunchCube", "enable": true, "inst_id": 14863, "file_id": 1, "layer": 0, "t": [0.0, 1.0520457029342651, 0.0], "r": [0.0, 0.0, 0.0, 1.0], "s": [1.0, 1.0, 1.0], "flags": 0, "is_static": false, "component_count": 1, "child_count": 2}}, {"type": "BLOCKMAN3.ParticleSystem", "data": {"materials": [{"exist": true, "sig": {"type": 2, "ptype": 2, "id": "86F49EBA64FE44ED96DBB7DE9440C25F"}, "shared": true}], "shadow_casting_mode": 1, "receive_shadows": true, "estimate_pixel_cull_type": 0, "custom_estimate_pixel_cull_value": 0.0, "inst_id": 14864, "file_id": 2, "enabled": true}}, {"type": "BLOCKMAN3.PSInvokeContext", "data": {"UVModule": {"Data": {"m_Enable": false, "m_Mode": 0, "m_TimeMode": 0, "m_FPS": 30.0, "m_SpeedRange": [0.0, 0.0], "m_TileCountX": 1, "m_TileCountY": 1, "m_AnimationType": 0, "m_RowIndex": 0, "m_CycleCount": 1.0, "m_UVChannelMask": -1, "m_RowMode": 1, "m_FrameOverTime": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_StartFrame": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_SpriteSequence": {"exist": false}}}}}, {"type": "BLOCKMAN3.PSColorModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}}}}, {"type": "BLOCKMAN3.PSColorByVelocityModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SPEED_RANGE": [0.0, 1.0]}}}, {"type": "BLOCKMAN3.PSEmissionModule", "data": {"DATA": {"ENABLE": false, "RATE_OVER_TIME": {"MAX_SCALAR": 10.0, "MIN_SCALAR": 10.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RATE_OVER_DISTANCE": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "BURSTS": []}}}, {"type": "BLOCKMAN3.PSForceModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "RANDOMIZE": false, "FORCE_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInitialModule", "data": {"DATA": {"LIFETIME": {"MAX_SCALAR": 5.0, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FLIP_ROTATION": 0.0, "MAX_PARTICLES": 0, "USE_3D_SIZE": false, "USE_3D_ROTATION": false}}}, {"type": "BLOCKMAN3.PSRotationByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSRotationModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSShapeModule", "data": {"DATA": {"ENABLE": false, "RADIUS": {"v": 1.0, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "ANGLE": 25.0, "LENGTH": 5.0, "RADIUS_THICKNESS": 1.0, "DONUT_THICKNESS": 0.20000000298023224, "BOX_THICKNESS": [0.0, 0.0, 0.0], "ARC": {"v": 360.0, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "POSITION": [0.0, 0.0, 0.0], "ROTATION": [0.0, 0.0, 0.0], "SCALE": [1.0, 1.0, 1.0], "TYPE": 2, "STYLE": 2, "ALIGN_TO_DIRECTION": false, "RANDOMIZE_DIRECTION": 0.0, "SPHERIZE_DIRECTION": 0.0, "RANDOMIZE_POSITION": 0.0}}}, {"type": "BLOCKMAN3.PSSizeByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSSizeModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSVelocityInheritModule", "data": {"DATA": {"ENABLE": false, "MODE": 0, "MULTIPLIER": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityLimitationModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "SEPARATE_AXES": false, "DRAG_MULTIPLY_BY_SIZE": true, "DRAG_MULTIPLY_BY_VELOCITY": true, "DAMPEN": 0.0, "VELOCITY": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DRAG": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "LINEAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RADIAL_VELOCITY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED_MODIFIER": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInvokeState", "data": {"START_DELAY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DELAY_ALL_LOOP": false, "DURATION": 1.5, "SIMULATION_SPEED": 1.0, "RANDOM_SEED": 1224476403, "AUTO_RANDOM_SEED": true, "LOOPING": false, "PLAY_ON_AWAKE": false, "USE_SCALED_TIME": true, "CULLING_MODE": 3, "SIMULATION_SPACE": 0, "SIMULATION_SPACE_CUSTOM": {"NULL": {"ref_type": 0}}, "EMITTER_VELOCITY_MODE": 1, "EMITTER_VELOCITY_CUSTOM": [0.0, 0.0, 0.0], "SCALING_MODE": 1}}, {"type": "BLOCKMAN3.PSRenderContext", "data": {"ENABLE": false, "STATE": {"RENDER_MODE": 0, "RENDER_SPACE": 3, "SORT_MODE": 0, "MAX_PARTICLE_SIZE": 0.5, "MIN_PARTICLE_SIZE": 0.0, "CAMERA_SCALE": 0.0, "VELOCITY_SCALE": 0.0, "LENGTH_SCALE": 2.0, "NORMAL_DIRECTION": 1.0, "PIVOT": [0.0, 0.0, 0.0], "FLIP": [0.0, 0.0, 0.0], "ALLOW_ROLL": true}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "trail", "enable": true, "inst_id": 14865, "file_id": 3, "layer": 0, "t": [0.0, 0.0, 0.0], "r": [0.0, 0.0, 0.0, 1.0], "s": [1.0, 1.0, 1.0], "flags": 0, "is_static": false, "component_count": 1, "child_count": 0}}, {"type": "BLOCKMAN3.ParticleSystem", "data": {"materials": [{"exist": true, "sig": {"type": 2, "ptype": 2, "id": "257910058D98456609A87DBB62B663B0"}, "shared": true}], "shadow_casting_mode": 1, "receive_shadows": true, "estimate_pixel_cull_type": 0, "custom_estimate_pixel_cull_value": 0.0, "inst_id": 14866, "file_id": 4, "enabled": true}}, {"type": "BLOCKMAN3.PSInvokeContext", "data": {"UVModule": {"Data": {"m_Enable": false, "m_Mode": 0, "m_TimeMode": 0, "m_FPS": 30.0, "m_SpeedRange": [0.0, 0.0], "m_TileCountX": 1, "m_TileCountY": 1, "m_AnimationType": 0, "m_RowIndex": 0, "m_CycleCount": 1.0, "m_UVChannelMask": -1, "m_RowMode": 1, "m_FrameOverTime": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_StartFrame": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_SpriteSequence": {"exist": false}}}}}, {"type": "BLOCKMAN3.PSColorModule", "data": {"DATA": {"ENABLE": true, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 2, "MAX_GRADIENT": {"mode": 0, "alpha": [{"time": 0.0, "value": 0.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 0.058173079043626785, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 0.7562500238418579, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 0.0, "inTangent": 0.0, "outTangent": 0.0}], "color": [[1.0, 1.0, 1.0, 0.0], [1.0, 1.0, 1.0, 1.0]]}}}}}, {"type": "BLOCKMAN3.PSColorByVelocityModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SPEED_RANGE": [0.0, 1.0]}}}, {"type": "BLOCKMAN3.PSEmissionModule", "data": {"DATA": {"ENABLE": true, "RATE_OVER_TIME": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 10.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RATE_OVER_DISTANCE": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "BURSTS": [{"TIME": 0.0, "COUNT": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 30.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "CYCLE": 1, "INTERVAL": 0.009999999776482582, "PROBABILITY": 1.0}]}}}, {"type": "BLOCKMAN3.PSForceModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "RANDOMIZE": false, "FORCE_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInitialModule", "data": {"DATA": {"LIFETIME": {"MAX_SCALAR": 1.5, "MIN_SCALAR": 1.2000000476837158, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED": {"MAX_SCALAR": 0.009999999776482582, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SIZE_X": {"MAX_SCALAR": 1.7999999523162842, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Y": {"MAX_SCALAR": 3.5, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Z": {"MAX_SCALAR": 1.7999999523162842, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FLIP_ROTATION": 0.0, "MAX_PARTICLES": 1000, "USE_3D_SIZE": true, "USE_3D_ROTATION": false}}}, {"type": "BLOCKMAN3.PSRotationByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSRotationModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSShapeModule", "data": {"DATA": {"ENABLE": true, "RADIUS": {"v": 0.009999999776482582, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "ANGLE": 25.0, "LENGTH": 5.0, "RADIUS_THICKNESS": 1.0, "DONUT_THICKNESS": 0.20000000298023224, "BOX_THICKNESS": [0.0, 0.0, 0.0], "ARC": {"v": 360.0, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "POSITION": [0.0, 0.0, 0.0], "ROTATION": [180.0, 0.0, 0.0], "SCALE": [1.0, 1.0, 1.0], "TYPE": 2, "STYLE": 2, "ALIGN_TO_DIRECTION": false, "RANDOMIZE_DIRECTION": 0.0, "SPHERIZE_DIRECTION": 0.0, "RANDOMIZE_POSITION": 0.0}}}, {"type": "BLOCKMAN3.PSSizeByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSSizeModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": true, "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 0.9933554530143738, "inTangent": 0.0, "outTangent": 0.009702491573989391}, {"time": 0.6848289370536804, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 0.8671095967292786, "inTangent": -0.42164531350135803, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0013003901112824678, "value": 0.029900332912802696, "inTangent": 1.94132661819458, "outTangent": 3.4873178005218506}, {"time": 0.22898824512958527, "value": 0.8239202499389648, "inTangent": 3.4873178005218506, "outTangent": 0.21975696086883545}, {"time": 1.0, "value": 0.9933554530143738, "inTangent": 0.21975696086883545, "outTangent": -2.000002155000402e-07}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 0.9933554530143738, "inTangent": 0.0, "outTangent": 0.010079103522002697}, {"time": 0.6592398881912231, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 0.7646293640136719, "value": 0.880398690700531, "inTangent": -1.1348506212234497, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSVelocityInheritModule", "data": {"DATA": {"ENABLE": false, "MODE": 0, "MULTIPLIER": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityLimitationModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "SEPARATE_AXES": false, "DRAG_MULTIPLY_BY_SIZE": true, "DRAG_MULTIPLY_BY_VELOCITY": true, "DAMPEN": 0.0, "VELOCITY": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DRAG": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "LINEAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RADIAL_VELOCITY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED_MODIFIER": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInvokeState", "data": {"START_DELAY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DELAY_ALL_LOOP": false, "DURATION": 1.5, "SIMULATION_SPEED": 1.0, "RANDOM_SEED": 1091888630, "AUTO_RANDOM_SEED": true, "LOOPING": false, "PLAY_ON_AWAKE": false, "USE_SCALED_TIME": true, "CULLING_MODE": 3, "SIMULATION_SPACE": 0, "SIMULATION_SPACE_CUSTOM": {"NULL": {"ref_type": 0}}, "EMITTER_VELOCITY_MODE": 1, "EMITTER_VELOCITY_CUSTOM": [0.0, 0.0, 0.0], "SCALING_MODE": 1}}, {"type": "BLOCKMAN3.PSRenderContext", "data": {"ENABLE": true, "STATE": {"RENDER_MODE": 3, "RENDER_SPACE": 3, "SORT_MODE": 0, "MAX_PARTICLE_SIZE": 0.5, "MIN_PARTICLE_SIZE": 0.0, "CAMERA_SCALE": 0.0, "VELOCITY_SCALE": 0.0, "LENGTH_SCALE": 2.0, "NORMAL_DIRECTION": 1.0, "PIVOT": [0.0, 0.0, 0.0], "FLIP": [0.0, 0.0, 0.0], "ALLOW_ROLL": true}}}, {"type": "BLOCKMAN3.GameObject", "data": {"name": "lizi", "enable": true, "inst_id": 14867, "file_id": 5, "layer": 0, "t": [0.0, 0.800000011920929, 6.224793480669177e-08], "r": [0.7071067690849304, 0.0, 0.0, 0.7071067690849304], "s": [1.0, 1.0, 1.0], "flags": 0, "is_static": false, "component_count": 1, "child_count": 0}}, {"type": "BLOCKMAN3.ParticleSystem", "data": {"materials": [{"exist": true, "sig": {"type": 2, "ptype": 0}, "shared": false, "data": {"head": {"_name": "lizi", "_type": 2}, "relys": ["4334AFB09B004C77749772AD6CFDCFE1"], "shader": "Engine/Default_Particle", "floats": [{"name": "_AlphaThreadHold", "value": 0.5}, {"name": "_EmissiveStrength", "value": 1.0}, {"name": "_Metallic", "value": 0.0}, {"name": "_NormalStrength", "value": 1.0}, {"name": "_Smoothness", "value": 0.5}], "vectors": [{"name": "_EmissiveColor", "value": [0.0, 0.0, 0.0, 1.0]}, {"name": "_EmissiveTex_ST", "value": [1.0, 1.0, 0.0, 0.0]}, {"name": "_NormalTex_ST", "value": [1.0, 1.0, 0.0, 0.0]}], "textures": [{"name": "_MainTex", "value": {"type": 5, "ptype": 2, "id": "4334AFB09B004C77749772AD6CFDCFE1"}}], "origin_material": {"type": 2, "ptype": 2, "id": "A1FEFFA09A3D43473DBFAC3939782CB1"}, "name": "lizi"}}], "shadow_casting_mode": 1, "receive_shadows": true, "estimate_pixel_cull_type": 0, "custom_estimate_pixel_cull_value": 0.0, "inst_id": 14868, "file_id": 6, "enabled": true}}, {"type": "BLOCKMAN3.PSInvokeContext", "data": {"UVModule": {"Data": {"m_Enable": false, "m_Mode": 0, "m_TimeMode": 0, "m_FPS": 30.0, "m_SpeedRange": [0.0, 0.0], "m_TileCountX": 1, "m_TileCountY": 1, "m_AnimationType": 0, "m_RowIndex": 0, "m_CycleCount": 1.0, "m_UVChannelMask": -1, "m_RowMode": 1, "m_FrameOverTime": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_StartFrame": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_SpriteSequence": {"exist": false}}}}}, {"type": "BLOCKMAN3.PSColorModule", "data": {"DATA": {"ENABLE": true, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 2, "MAX_GRADIENT": {"mode": 0, "alpha": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 0.4838942587375641, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 0.0, "inTangent": 0.0, "outTangent": 0.0}], "color": [[1.0, 1.0, 1.0, 0.0], [1.0, 1.0, 1.0, 1.0]]}}}}}, {"type": "BLOCKMAN3.PSColorByVelocityModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SPEED_RANGE": [0.0, 1.0]}}}, {"type": "BLOCKMAN3.PSEmissionModule", "data": {"DATA": {"ENABLE": true, "RATE_OVER_TIME": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 10.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RATE_OVER_DISTANCE": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "BURSTS": [{"TIME": 0.0, "COUNT": {"MAX_SCALAR": 10.0, "MIN_SCALAR": 30.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "CYCLE": 1, "INTERVAL": 0.009999999776482582, "PROBABILITY": 1.0}]}}}, {"type": "BLOCKMAN3.PSForceModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "RANDOMIZE": false, "FORCE_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInitialModule", "data": {"DATA": {"LIFETIME": {"MAX_SCALAR": 1.5, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED": {"MAX_SCALAR": 10.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 3, "MAX_GRADIENT": {"mode": 0, "alpha": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}], "color": [[1.0, 0.8666666746139526, 0.0, 0.08990384638309479], [1.0, 0.0, 0.0, 0.2670673131942749], [1.0, 1.0, 1.0, 0.5050480961799622], [1.0, 1.0, 1.0, 0.7430288791656494], [0.08235294371843338, 1.0, 0.0, 1.0]]}, "MIN_GRADIENT": {"mode": 0, "alpha": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}], "color": [[1.0, 0.0, 0.0, 0.0], [0.0, 0.3490196168422699, 1.0, 0.30144232511520386], [1.0, 1.0, 1.0, 0.6531250476837158], [0.0, 1.0, 0.13333334028720856, 1.0]]}}, "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 0.5, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1}, "ROTATION_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FLIP_ROTATION": 0.0, "MAX_PARTICLES": 1000, "USE_3D_SIZE": false, "USE_3D_ROTATION": false}}}, {"type": "BLOCKMAN3.PSRotationByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSRotationModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSShapeModule", "data": {"DATA": {"ENABLE": true, "RADIUS": {"v": 0.12999999523162842, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "ANGLE": 25.0, "LENGTH": 5.0, "RADIUS_THICKNESS": 1.0, "DONUT_THICKNESS": 0.20000000298023224, "BOX_THICKNESS": [0.0, 0.0, 0.0], "ARC": {"v": 360.0, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "POSITION": [0.0, 0.0, 0.0], "ROTATION": [0.0, 0.0, 0.0], "SCALE": [1.0, 1.0, 1.0], "TYPE": 2, "STYLE": 2, "ALIGN_TO_DIRECTION": false, "RANDOMIZE_DIRECTION": 0.0, "SPHERIZE_DIRECTION": 0.0, "RANDOMIZE_POSITION": 0.0}}}, {"type": "BLOCKMAN3.PSSizeByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSSizeModule", "data": {"DATA": {"ENABLE": true, "SEPARATE_AXES": false, "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": -6.195431709289551, "outTangent": -7.3773932456970215}, {"time": 0.11573473364114761, "value": 0.14617937803268433, "inTangent": -7.3773932456970215, "outTangent": 4.528995037078857}, {"time": 0.2873862087726593, "value": 0.9235880374908447, "inTangent": 4.528995037078857, "outTangent": -4.760257720947266}, {"time": 0.4395318627357483, "value": 0.199335515499115, "inTangent": -4.760257720947266, "outTangent": 3.4116156101226807}, {"time": 0.6527958512306213, "value": 0.9269102811813354, "inTangent": 3.4116156101226807, "outTangent": -2.660071849822998}, {"time": 1.0, "value": 0.003322259057313204, "inTangent": -2.660071849822998, "outTangent": -6.2834696769714355}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSVelocityInheritModule", "data": {"DATA": {"ENABLE": false, "MODE": 0, "MULTIPLIER": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityLimitationModule", "data": {"DATA": {"ENABLE": true, "SPACE": 0, "SEPARATE_AXES": false, "DRAG_MULTIPLY_BY_SIZE": true, "DRAG_MULTIPLY_BY_VELOCITY": true, "DAMPEN": 0.5, "VELOCITY": {"MAX_SCALAR": 1.5, "MIN_SCALAR": 0.20000000298023224, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1}, "VELOCITY_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DRAG": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "LINEAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RADIAL_VELOCITY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED_MODIFIER": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInvokeState", "data": {"START_DELAY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DELAY_ALL_LOOP": false, "DURATION": 1.5, "SIMULATION_SPEED": 1.0, "RANDOM_SEED": 1397535851, "AUTO_RANDOM_SEED": true, "LOOPING": false, "PLAY_ON_AWAKE": false, "USE_SCALED_TIME": true, "CULLING_MODE": 3, "SIMULATION_SPACE": 0, "SIMULATION_SPACE_CUSTOM": {"NULL": {"ref_type": 0}}, "EMITTER_VELOCITY_MODE": 1, "EMITTER_VELOCITY_CUSTOM": [0.0, 0.0, 0.0], "SCALING_MODE": 1}}, {"type": "BLOCKMAN3.PSRenderContext", "data": {"ENABLE": true, "STATE": {"RENDER_MODE": 0, "RENDER_SPACE": 3, "SORT_MODE": 0, "MAX_PARTICLE_SIZE": 0.5, "MIN_PARTICLE_SIZE": 0.0, "CAMERA_SCALE": 0.0, "VELOCITY_SCALE": 0.0, "LENGTH_SCALE": 2.0, "NORMAL_DIRECTION": 1.0, "PIVOT": [0.0, 0.0, 0.0], "FLIP": [0.0, 0.0, 0.0], "ALLOW_ROLL": true}}}]}, "head": {"_name": "", "_type": 1}, "relys": ["A1FEFFA09A3D43473DBFAC3939782CB1", "86F49EBA64FE44ED96DBB7DE9440C25F", "257910058D98456609A87DBB62B663B0", "00903E6839EF478E31A7BE018CBC1463", "4334AFB09B004C77749772AD6CFDCFE1", "188FDD8FE9454E24C4A5BB9DFD143CEE"]}