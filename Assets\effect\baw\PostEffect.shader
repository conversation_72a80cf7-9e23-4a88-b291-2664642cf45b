#pragma name "Game/PostEffect"
#pragma vertex vert
#pragma fragment frag
#pragma property texture2D _MainTex
// #pragma property color _Color (1)
#pragma using MatrixMVP

#pragma ztest always
#pragma zwrite off

#pragma shader_feature ENABLE_EFFECT_GRAY

struct vrt {
    float4 pos : POSITION;
    half2 uv : TEXCOORD0;
};

struct v2f {
    float4 pos : SV_POSITION;
    half2 uv : TEXCOORD0;
}; 

v2f vert(vrt i) {
    v2f v;
    v.pos = float4((i.pos.xy * 2.),0.,1.);
    v.uv = i.uv;
    return v;
}

half4 frag(v2f f) {
    vec4 main_tex_col = tex2D(_MainTex, f.uv);
    vec4 result = main_tex_col;
#ifdef ENABLE_EFFECT_GRAY
    float gray = 0.114 * result.r + 0.587 * result.g + 0.299 * result.b;
    result.rgb = vec3(gray);
#endif
    return result;
}