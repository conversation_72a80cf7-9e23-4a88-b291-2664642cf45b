{"prefab": {"block_count": 18, "file_map_identity": 3, "objects": [{"type": "BLOCKMAN3.GameObject", "data": {"name": "bigbasicharm", "enable": true, "inst_id": 86879, "file_id": 1, "layer": 0, "t": [0.0, 0.0, 0.0], "r": [0.0, 0.0, 0.0, 1.0], "s": [1.0, 1.0000001192092896, 1.0], "flags": 0, "is_static": false, "component_count": 1, "child_count": 0}}, {"type": "BLOCKMAN3.ParticleSystem", "data": {"materials": [{"exist": true, "sig": {"type": 2, "ptype": 0}, "shared": false, "data": {"head": {"_name": "bigbasicharm", "_type": 2}, "relys": ["F57E4DC8DF324CC2F6BEC4435D22E19A"], "shader": "Engine/Default_Particle", "floats": [{"name": "_AlphaThreadHold", "value": 0.5}, {"name": "_EmissiveStrength", "value": 1.0}, {"name": "_Metallic", "value": 0.0}, {"name": "_NormalStrength", "value": 1.0}, {"name": "_Smoothness", "value": 0.5}], "vectors": [{"name": "_EmissiveColor", "value": [0.0, 0.0, 0.0, 1.0]}, {"name": "_EmissiveTex_ST", "value": [1.0, 1.0, 0.0, 0.0]}, {"name": "_NormalTex_ST", "value": [1.0, 1.0, 0.0, 0.0]}], "textures": [{"name": "_MainTex", "value": {"type": 5, "ptype": 2, "id": "F57E4DC8DF324CC2F6BEC4435D22E19A"}}], "origin_material": {"type": 2, "ptype": 2, "id": "A041A296766F45AB3D93211FC3D03307"}, "name": "bigbasicharm"}}], "shadow_casting_mode": 1, "receive_shadows": true, "estimate_pixel_cull_type": 0, "custom_estimate_pixel_cull_value": 0.0, "inst_id": 86880, "file_id": 2, "enabled": true}}, {"type": "BLOCKMAN3.PSInvokeContext", "data": {"UVModule": {"Data": {"m_Enable": true, "m_Mode": 0, "m_TimeMode": 0, "m_FPS": 30.0, "m_SpeedRange": [0.0, 0.0], "m_TileCountX": 4, "m_TileCountY": 4, "m_AnimationType": 0, "m_RowIndex": 0, "m_CycleCount": 2.0, "m_UVChannelMask": -1, "m_RowMode": 1, "m_FrameOverTime": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": -1.7102793455123901, "outTangent": -1.7102793455123901}, {"time": 1.0, "value": 0.0, "inTangent": -0.5618309378623962, "outTangent": -0.5618309378623962}]}}, "m_StartFrame": {"MAX_SCALAR": 0.5, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "m_SpriteSequence": {"exist": false}}}}}, {"type": "BLOCKMAN3.PSColorModule", "data": {"DATA": {"ENABLE": true, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 2, "MAX_GRADIENT": {"mode": 0, "alpha": [{"time": 0.0, "value": 0.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 0.07403846085071564, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 0.9413461685180664, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 0.0, "inTangent": 0.0, "outTangent": 0.0}], "color": [[1.0, 1.0, 1.0, 0.0], [1.0, 0.9333333373069763, 0.9333333373069763, 1.0]]}}}}}, {"type": "BLOCKMAN3.PSColorByVelocityModule", "data": {"DATA": {"ENABLE": false, "COLOR": {"MAX_COLOR": [1.0, 1.0, 1.0, 1.0], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SPEED_RANGE": [0.0, 1.0]}}}, {"type": "BLOCKMAN3.PSEmissionModule", "data": {"DATA": {"ENABLE": true, "RATE_OVER_TIME": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 10.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RATE_OVER_DISTANCE": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "BURSTS": [{"TIME": 0.0, "COUNT": {"MAX_SCALAR": 2.0, "MIN_SCALAR": 30.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "CYCLE": 1, "INTERVAL": 0.009999999776482582, "PROBABILITY": 1.0}]}}}, {"type": "BLOCKMAN3.PSForceModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "RANDOMIZE": false, "FORCE_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "FORCE_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInitialModule", "data": {"DATA": {"LIFETIME": {"MAX_SCALAR": 6.0, "MIN_SCALAR": 5.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "COLOR": {"MAX_COLOR": [0.729411780834198, 0.4274509847164154, 0.0784313753247261, 0.4274509847164154], "MIN_COLOR": [1.0, 1.0, 1.0, 1.0], "MODE": 0}, "SIZE_X": {"MAX_SCALAR": 18.0, "MIN_SCALAR": 4.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Y": {"MAX_SCALAR": 1.5099999904632568, "MIN_SCALAR": 1.809999942779541, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ROTATION_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1}, "ROTATION_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1}, "ROTATION_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1}, "FLIP_ROTATION": 0.0, "MAX_PARTICLES": 1000, "USE_3D_SIZE": false, "USE_3D_ROTATION": false}}}, {"type": "BLOCKMAN3.PSRotationByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "ANGULAR_VELOCITY_X": {"MAX_SCALAR": -10.0, "MIN_SCALAR": 20.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 1}}}}, {"type": "BLOCKMAN3.PSRotationModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "ANGULAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ANGULAR_VELOCITY_Z": {"MAX_SCALAR": 0.7853981852531433, "MIN_SCALAR": 0.7853981852531433, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSShapeModule", "data": {"DATA": {"ENABLE": true, "RADIUS": {"v": 9.999999747378752e-05, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "ANGLE": 57.150001525878906, "LENGTH": 5.0, "RADIUS_THICKNESS": 1.0, "DONUT_THICKNESS": 0.20000000298023224, "BOX_THICKNESS": [0.0, 0.0, 0.0], "ARC": {"v": 360.0, "m": 0, "c": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "s": 0.0}, "POSITION": [0.0, 0.0, 0.0], "ROTATION": [0.0, 0.0, 0.0], "SCALE": [1.0, 1.0, 1.0399999618530273], "TYPE": 2, "STYLE": 2, "ALIGN_TO_DIRECTION": false, "RANDOMIZE_DIRECTION": 0.0, "SPHERIZE_DIRECTION": 0.0, "RANDOMIZE_POSITION": 0.0}}}, {"type": "BLOCKMAN3.PSSizeByVelocityModule", "data": {"DATA": {"ENABLE": false, "SEPARATE_AXES": false, "SPEED_RANGE": [0.0, 1.0], "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSSizeModule", "data": {"DATA": {"ENABLE": true, "SEPARATE_AXES": false, "SIZE_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 0.8737541437149048, "inTangent": 0.0, "outTangent": 0.0}, {"time": 0.47963210940361023, "value": 0.8372093439102173, "inTangent": -0.011781814508140087, "outTangent": -0.011781814508140087}, {"time": 1.0, "value": 0.8338870406150818, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}, "SIZE_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 2, "MAX_CURVE": {"curve": [{"time": 0.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}, {"time": 1.0, "value": 1.0, "inTangent": 0.0, "outTangent": 0.0}]}}}}}, {"type": "BLOCKMAN3.PSVelocityInheritModule", "data": {"DATA": {"ENABLE": false, "MODE": 0, "MULTIPLIER": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityLimitationModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "SEPARATE_AXES": false, "DRAG_MULTIPLY_BY_SIZE": true, "DRAG_MULTIPLY_BY_VELOCITY": true, "DAMPEN": 0.0, "VELOCITY": {"MAX_SCALAR": 1.9900000095367432, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_X": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Y": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "VELOCITY_Z": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DRAG": {"MAX_SCALAR": 1.6699999570846558, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSVelocityModule", "data": {"DATA": {"ENABLE": false, "SPACE": 0, "LINEAR_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "LINEAR_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "ORBITAL_VELOCITY_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "RADIAL_VELOCITY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_X": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Y": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "OFFSET_Z": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "SPEED_MODIFIER": {"MAX_SCALAR": 1.0, "MIN_SCALAR": 1.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}}}}, {"type": "BLOCKMAN3.PSInvokeState", "data": {"START_DELAY": {"MAX_SCALAR": 0.0, "MIN_SCALAR": 0.0, "PRE_MAX_CURVE_WRAP_MODE": 0, "POST_MAX_CURVE_WRAP_MODE": 0, "PRE_MIN_CURVE_WRAP_MODE": 0, "POST_MIN_CURVE_WRAP_MODE": 0, "MODE": 0}, "DELAY_ALL_LOOP": false, "DURATION": 3.0, "SIMULATION_SPEED": 1.5, "RANDOM_SEED": 334646067, "AUTO_RANDOM_SEED": true, "LOOPING": true, "PLAY_ON_AWAKE": true, "USE_SCALED_TIME": true, "CULLING_MODE": 3, "SIMULATION_SPACE": 0, "SIMULATION_SPACE_CUSTOM": {"NULL": {"ref_type": 0}}, "EMITTER_VELOCITY_MODE": 1, "EMITTER_VELOCITY_CUSTOM": [0.0, 0.0, 0.0], "SCALING_MODE": 1}}, {"type": "BLOCKMAN3.PSRenderContext", "data": {"ENABLE": true, "STATE": {"RENDER_MODE": 0, "RENDER_SPACE": 3, "SORT_MODE": 0, "MAX_PARTICLE_SIZE": 0.5, "MIN_PARTICLE_SIZE": 0.0, "CAMERA_SCALE": 0.0, "VELOCITY_SCALE": 0.0, "LENGTH_SCALE": 2.0, "NORMAL_DIRECTION": 1.0, "PIVOT": [0.0, 0.0, 0.0], "FLIP": [0.0, 0.0, 0.0], "ALLOW_ROLL": true}}}]}, "head": {"_name": "", "_type": 1}, "relys": ["A041A296766F45AB3D93211FC3D03307", "F57E4DC8DF324CC2F6BEC4435D22E19A"]}